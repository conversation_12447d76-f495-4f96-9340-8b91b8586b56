/**
 * Image Optimization Utilities
 * Provides utilities for image optimization, CDN integration, and responsive images
 */

export interface ImageOptimizationOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'webp' | 'avif' | 'png' | 'jpg' | 'jpeg' | 'auto';
  fit?: 'contain' | 'cover' | 'fill' | 'inside' | 'outside';
  crop?: 'center' | 'top' | 'bottom' | 'left' | 'right' | 'smart';
  blur?: number;
  brightness?: number;
  contrast?: number;
  saturation?: number;
  sharpen?: number;
}

export interface ResponsiveBreakpoints {
  mobile?: number;
  tablet?: number;
  desktop?: number;
  wide?: number;
}

/**
 * Default responsive breakpoints
 */
export const DEFAULT_BREAKPOINTS: ResponsiveBreakpoints = {
  mobile: 480,
  tablet: 768,
  desktop: 1200,
  wide: 1600
};

/**
 * Check if URL is from Bunny CDN
 */
export function isBunnyCDN(url: string): boolean {
  return url.includes('bunnycdn.com') || url.includes('b-cdn.net');
}

/**
 * Check if URL is external (not local asset)
 */
export function isExternalImage(url: string): boolean {
  return url.startsWith('http') || url.startsWith('//');
}

/**
 * Build Bunny CDN optimization parameters
 */
export function buildBunnyCDNParams(options: ImageOptimizationOptions = {}): string {
  const params = new URLSearchParams();
  
  // Core parameters
  if (options.width) params.set('width', options.width.toString());
  if (options.height) params.set('height', options.height.toString());
  if (options.quality) params.set('quality', options.quality.toString());
  if (options.format && options.format !== 'auto') params.set('format', options.format);
  if (options.fit) params.set('fit', options.fit);
  if (options.crop) params.set('crop', options.crop);
  
  // Enhancement parameters
  if (options.blur !== undefined) params.set('blur', options.blur.toString());
  if (options.brightness !== undefined) params.set('brightness', options.brightness.toString());
  if (options.contrast !== undefined) params.set('contrast', options.contrast.toString());
  if (options.saturation !== undefined) params.set('saturation', options.saturation.toString());
  if (options.sharpen !== undefined) params.set('sharpen', options.sharpen.toString());
  
  return params.toString();
}

/**
 * Generate optimized URL for Bunny CDN
 */
export function optimizeBunnyCDNUrl(
  baseUrl: string, 
  options: ImageOptimizationOptions = {}
): string {
  const cleanUrl = baseUrl.split('?')[0]; // Remove existing params
  const params = buildBunnyCDNParams(options);
  return params ? `${cleanUrl}?${params}` : cleanUrl;
}

/**
 * Generate responsive image URLs for different breakpoints
 */
export function generateResponsiveUrls(
  baseUrl: string,
  originalWidth: number,
  originalHeight: number,
  breakpoints: ResponsiveBreakpoints = DEFAULT_BREAKPOINTS,
  options: Omit<ImageOptimizationOptions, 'width' | 'height'> = {}
): Record<string, string> {
  const aspectRatio = originalWidth / originalHeight;
  const urls: Record<string, string> = {};
  
  Object.entries(breakpoints).forEach(([key, width]) => {
    if (width) {
      const height = Math.round(width / aspectRatio);
      urls[key] = optimizeBunnyCDNUrl(baseUrl, {
        ...options,
        width,
        height
      });
    }
  });
  
  // Add default size
  urls.default = optimizeBunnyCDNUrl(baseUrl, {
    ...options,
    width: originalWidth,
    height: originalHeight
  });
  
  return urls;
}

/**
 * Generate srcset string for responsive images
 */
export function generateSrcSet(
  baseUrl: string,
  originalWidth: number,
  originalHeight: number,
  breakpoints: ResponsiveBreakpoints = DEFAULT_BREAKPOINTS,
  options: Omit<ImageOptimizationOptions, 'width' | 'height'> = {}
): string {
  const urls = generateResponsiveUrls(baseUrl, originalWidth, originalHeight, breakpoints, options);
  
  return Object.entries(breakpoints)
    .filter(([key, width]) => width && urls[key as keyof typeof urls])
    .map(([key, width]) => `${urls[key as keyof typeof urls]} ${width}w`)
    .join(', ');
}

/**
 * Generate low-quality placeholder image URL
 */
export function generatePlaceholder(
  baseUrl: string,
  width: number = 50,
  height: number = 50
): string {
  return optimizeBunnyCDNUrl(baseUrl, {
    width,
    height,
    quality: 30,
    blur: 10,
    format: 'jpg' // JPG is smaller for blurred images
  });
}

/**
 * Get optimal image format based on browser support
 */
export function getOptimalFormat(): 'webp' | 'avif' | 'jpg' {
  if (typeof window === 'undefined') return 'webp'; // SSR fallback
  
  // Check for AVIF support
  const avifCanvas = document.createElement('canvas');
  avifCanvas.width = 1;
  avifCanvas.height = 1;
  const avifSupported = avifCanvas.toDataURL('image/avif').indexOf('data:image/avif') === 0;
  
  if (avifSupported) return 'avif';
  
  // Check for WebP support
  const webpCanvas = document.createElement('canvas');
  webpCanvas.width = 1;
  webpCanvas.height = 1;
  const webpSupported = webpCanvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  
  return webpSupported ? 'webp' : 'jpg';
}

/**
 * Calculate aspect ratio from dimensions
 */
export function calculateAspectRatio(width: number, height: number): number {
  return width / height;
}

/**
 * Calculate dimensions maintaining aspect ratio
 */
export function calculateDimensions(
  originalWidth: number,
  originalHeight: number,
  targetWidth?: number,
  targetHeight?: number
): { width: number; height: number } {
  const aspectRatio = calculateAspectRatio(originalWidth, originalHeight);
  
  if (targetWidth && targetHeight) {
    return { width: targetWidth, height: targetHeight };
  }
  
  if (targetWidth) {
    return { width: targetWidth, height: Math.round(targetWidth / aspectRatio) };
  }
  
  if (targetHeight) {
    return { width: Math.round(targetHeight * aspectRatio), height: targetHeight };
  }
  
  return { width: originalWidth, height: originalHeight };
}

/**
 * Preload critical images
 */
export function preloadImage(src: string, options: { as?: string; crossorigin?: string } = {}): void {
  if (typeof window === 'undefined') return;
  
  const link = document.createElement('link');
  link.rel = 'preload';
  link.as = options.as || 'image';
  link.href = src;
  
  if (options.crossorigin) {
    link.crossOrigin = options.crossorigin;
  }
  
  document.head.appendChild(link);
}

/**
 * Lazy load images with Intersection Observer
 */
export function setupLazyLoading(selector: string = 'img[loading="lazy"]'): void {
  if (typeof window === 'undefined' || !('IntersectionObserver' in window)) return;
  
  const images = document.querySelectorAll(selector);
  
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement;
        
        if (img.dataset.src) {
          img.src = img.dataset.src;
          img.removeAttribute('data-src');
        }
        
        if (img.dataset.srcset) {
          img.srcset = img.dataset.srcset;
          img.removeAttribute('data-srcset');
        }
        
        img.classList.remove('lazy');
        observer.unobserve(img);
      }
    });
  }, {
    rootMargin: '50px 0px',
    threshold: 0.01
  });
  
  images.forEach(img => imageObserver.observe(img));
}
