/**
 * Google Analytics utility functions for tracking events
 * Only works in production and when gtag is available
 */

declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
  }
}

/**
 * Track a custom event in Google Analytics
 * @param eventName - The name of the event
 * @param parameters - Additional parameters for the event
 */
export function trackEvent(eventName: string, parameters: Record<string, any> = {}) {
  // Only track in production and if gtag is available
  if (typeof window !== 'undefined' && window.gtag && import.meta.env.PROD) {
    try {
      window.gtag('event', eventName, {
        event_category: parameters.category || 'User Interaction',
        event_label: parameters.label,
        value: parameters.value,
        non_interaction: parameters.non_interaction || false,
        ...parameters
      });
    } catch (error) {
      console.warn('Analytics tracking failed:', error);
    }
  }
}

/**
 * Track page views (usually handled automatically by GA4)
 * @param pagePath - The page path to track
 * @param pageTitle - The page title
 */
export function trackPageView(pagePath: string, pageTitle?: string) {
  if (typeof window !== 'undefined' && window.gtag && import.meta.env.PROD) {
    try {
      window.gtag('config', 'G-5FH6Y2MPJN', {
        page_path: pagePath,
        page_title: pageTitle
      });
    } catch (error) {
      console.warn('Page view tracking failed:', error);
    }
  }
}

/**
 * Track ecommerce events for product interactions
 */
export const ecommerce = {
  /**
   * Track when a user views a product
   */
  viewItem: (productId: string, productName: string, category: string, price: number) => {
    trackEvent('view_item', {
      currency: 'USD',
      value: price,
      items: [{
        item_id: productId,
        item_name: productName,
        category: category,
        price: price,
        quantity: 1
      }]
    });
  },

  /**
   * Track when a user adds an item to cart
   */
  addToCart: (productId: string, productName: string, category: string, price: number) => {
    trackEvent('add_to_cart', {
      currency: 'USD',
      value: price,
      items: [{
        item_id: productId,
        item_name: productName,
        category: category,
        price: price,
        quantity: 1
      }]
    });
  },

  /**
   * Track when a user begins checkout
   */
  beginCheckout: (items: Array<{id: string, name: string, category: string, price: number, quantity: number}>) => {
    const totalValue = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    
    trackEvent('begin_checkout', {
      currency: 'USD',
      value: totalValue,
      items: items.map(item => ({
        item_id: item.id,
        item_name: item.name,
        category: item.category,
        price: item.price,
        quantity: item.quantity
      }))
    });
  },

  /**
   * Track successful purchases
   */
  purchase: (transactionId: string, items: Array<{id: string, name: string, category: string, price: number, quantity: number}>) => {
    const totalValue = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    
    trackEvent('purchase', {
      transaction_id: transactionId,
      currency: 'USD',
      value: totalValue,
      items: items.map(item => ({
        item_id: item.id,
        item_name: item.name,
        category: item.category,
        price: item.price,
        quantity: item.quantity
      }))
    });
  }
};

/**
 * Track user engagement events
 */
export const engagement = {
  /**
   * Track when user scrolls to a certain percentage of the page
   */
  scrollDepth: (percentage: number) => {
    trackEvent('scroll', {
      category: 'Engagement',
      label: `${percentage}%`,
      value: percentage,
      non_interaction: true
    });
  },

  /**
   * Track time spent on page
   */
  timeOnPage: (seconds: number) => {
    trackEvent('time_on_page', {
      category: 'Engagement',
      value: seconds,
      non_interaction: true
    });
  },

  /**
   * Track search queries
   */
  search: (query: string, resultsCount: number) => {
    trackEvent('search', {
      search_term: query,
      category: 'Site Search',
      value: resultsCount
    });
  }
};
