---
/**
 * LCP Image Optimizer Component
 * Specifically designed to optimize the Largest Contentful Paint element
 */

export interface Props {
  src: string;
  alt: string;
  width: number;
  height: number;
  mobileUrl?: string;
  tabletUrl?: string;
  priority?: boolean;
  className?: string;
  id?: string;
}

const {
  src,
  alt,
  width,
  height,
  mobileUrl,
  tabletUrl,
  priority = true,
  className = '',
  id = 'lcp-image'
} = Astro.props;

// Generate optimized srcset
const srcset = `
  ${mobileUrl || src} 300w,
  ${tabletUrl || src} 400w,
  ${src} 500w
`.trim();

const sizes = "(max-width: 768px) 300px, (max-width: 1200px) 400px, 500px";
---

<!-- Critical LCP Image Preload -->
{priority && (
  <link 
    rel="preload" 
    as="image" 
    href={src}
    imagesrcset={srcset}
    imagesizes={sizes}
    fetchpriority="high"
  />
)}

<!-- Ultra-Critical Inline Styles for LCP -->
<style is:inline define:vars={{ elementId: id, aspectRatio: `${width}/${height}` }}>
  #lcp-image, .lcp-image {
    /* Force immediate visibility */
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;

    /* Remove ALL render-blocking effects */
    transition: none !important;
    animation: none !important;
    transform: none !important;
    filter: none !important;

    /* Optimize rendering */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;

    /* Exact layout */
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;

    /* Hardware acceleration */
    will-change: auto;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;

    /* Prevent any delays */
    pointer-events: auto;
    user-select: auto;
  }

  /* Minimal container - let existing CSS handle layout */
  .lcp-container {
    position: relative;
    width: 100%;
    height: 100%;
  }
</style>

<!-- LCP Image with Maximum Optimization -->
<div class="lcp-container">
  <img
    src={src}
    srcset={srcset}
    sizes={sizes}
    alt={alt}
    class={`lcp-image ${className}`}
    id={id}
    width={width}
    height={height}
    loading="eager"
    fetchpriority="high"
    decoding="sync"
    style="width: 100%; height: 100%; object-fit: cover; opacity: 1; visibility: visible;"
    crossorigin="anonymous"
    onload="this.style.opacity='1'; this.setAttribute('data-loaded', 'true');"
    onerror="this.style.opacity='0.5'; this.setAttribute('data-error', 'true');"
  />
</div>

<!-- Critical LCP Optimization Script -->
<script is:inline define:vars={{ imageId: id, imageSrc: src }}>
  (function() {
    'use strict';
    
    // Ultra-high priority image preloading
    function preloadLCPImage() {
      if (!imageSrc) return;
      
      const img = new Image();
      img.src = imageSrc;
      img.fetchPriority = 'high';
      img.loading = 'eager';
      img.decoding = 'sync';
      
      // Force decode if supported
      if (img.decode) {
        img.decode().catch(() => {
          console.warn('LCP image decode failed, but continuing...');
        });
      }
    }
    
    // Optimize LCP element immediately
    function optimizeLCPElement() {
      const lcpElement = document.getElementById(imageId);
      if (lcpElement) {
        // Force immediate visibility
        lcpElement.style.opacity = '1';
        lcpElement.style.visibility = 'visible';
        lcpElement.style.display = 'block';
        
        // Remove any potential blocking
        lcpElement.style.transition = 'none';
        lcpElement.style.animation = 'none';
        lcpElement.style.transform = 'none';
        lcpElement.style.filter = 'none';
        
        // Mark as optimized
        lcpElement.setAttribute('data-lcp-optimized', 'true');
      }
    }
    
    // Run optimizations immediately
    preloadLCPImage();
    
    // Optimize on DOM ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', optimizeLCPElement);
    } else {
      optimizeLCPElement();
    }
    
    // Fallback optimization
    setTimeout(optimizeLCPElement, 0);
  })();
</script>

<!-- Performance monitoring for LCP -->
<script is:inline define:vars={{ elementId: id }}>
  // Monitor LCP performance
  if ('PerformanceObserver' in window) {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];

        if (lastEntry && lastEntry.element && lastEntry.element.id === elementId) {
          console.log(`LCP optimized image rendered in ${lastEntry.startTime.toFixed(2)}ms`);

          // Mark successful LCP
          if (lastEntry.element) {
            lastEntry.element.setAttribute('data-lcp-time', lastEntry.startTime.toFixed(2));
          }
        }
      });

      observer.observe({ type: 'largest-contentful-paint', buffered: true });
    } catch (error) {
      console.warn('LCP monitoring failed:', error);
    }
  }
</script>

<!--
Usage Example:

<LCPImageOptimizer
  src="https://cdn.example.com/product.jpg"
  alt="Product Name"
  width={500}
  height={375}
  mobileUrl="https://cdn.example.com/product-mobile.jpg"
  tabletUrl="https://cdn.example.com/product-tablet.jpg"
  id="main-product-image"
  className="product-hero-image"
/>
-->
