/* CSS Custom Properties (Variables) */
:root {
  --primary: #92400e;
  --primary-dark: #78350f;
  --primary-light: #d97706;
  --secondary: #1e293b;
  --secondary-light: #334155;
  --background: #f8fafc;
  --light-background: #ffffff;
  --card-bg: #ffffff;
  --text: #0f172a;
  --text-secondary: #475569;
  --muted: #64748b;
  --border: #e2e8f0;
  --border-light: #f1f5f9;
  --radius: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --container-width: 1200px;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Shared button properties to reduce duplication */
  --btn-padding: 0.75rem 1.5rem;
  --btn-padding-sm: 0.5rem 1rem;
  --btn-padding-lg: 1rem 2rem;
  --btn-font-size: 0.875rem;
  --btn-font-size-sm: 0.75rem;
  --btn-font-size-lg: 1rem;
  --btn-font-weight: 600;
  --btn-border-radius: var(--radius);
  --btn-transition: all 0.2s ease;
  --btn-hover-transform: translateY(-1px);
  --btn-hover-shadow: var(--shadow-md);

  /* Status colors */
  --success: #16a34a;
  --success-dark: #15803d;
  --danger: #dc2626;
  --danger-dark: #b91c1c;
  --warning: #d97706;
  --warning-dark: #b45309;

  /* Font loading optimization */
  --font-system: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-serif: Georgia, 'Times New Roman', Times, serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}
