// Performance Test Script for Cheers Marketplace
// Run this in the browser console to test current performance

console.log('🚀 Testing Performance Optimizations...\n');

// Test 1: Check CSS Loading
function testCSSLoading() {
  console.log('1. Testing CSS Loading Strategy...');
  
  const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
  const inlineStyles = document.querySelectorAll('style');
  
  console.log(`   • External stylesheets: ${stylesheets.length}`);
  console.log(`   • Inline styles: ${inlineStyles.length}`);
  
  // Check for render-blocking CSS
  const renderBlockingCSS = Array.from(stylesheets).filter(link => 
    !link.hasAttribute('media') || link.getAttribute('media') === 'all'
  );
  
  if (renderBlockingCSS.length > 0) {
    console.log(`   ⚠️  ${renderBlockingCSS.length} potentially render-blocking CSS files found:`);
    renderBlockingCSS.forEach(link => {
      console.log(`      - ${link.href}`);
    });
  } else {
    console.log('   ✅ No render-blocking CSS detected');
  }
}

// Test 2: Check JavaScript Loading
function testJavaScriptLoading() {
  console.log('\n2. Testing JavaScript Loading...');
  
  const scripts = document.querySelectorAll('script[src]');
  const inlineScripts = document.querySelectorAll('script:not([src])');
  
  console.log(`   • External scripts: ${scripts.length}`);
  console.log(`   • Inline scripts: ${inlineScripts.length}`);
  
  // Check for async/defer attributes
  const blockingScripts = Array.from(scripts).filter(script => 
    !script.hasAttribute('async') && !script.hasAttribute('defer')
  );
  
  if (blockingScripts.length > 0) {
    console.log(`   ⚠️  ${blockingScripts.length} potentially blocking scripts found:`);
    blockingScripts.forEach(script => {
      console.log(`      - ${script.src}`);
    });
  } else {
    console.log('   ✅ All scripts are non-blocking');
  }
}

// Test 3: Check Snipcart Integration
function testSnipcartIntegration() {
  console.log('\n3. Testing Snipcart Integration...');
  
  // Check if Snipcart settings are loaded
  if (window.SnipcartSettings) {
    console.log('   ✅ Snipcart settings loaded');
    console.log(`   • Load strategy: ${window.SnipcartSettings.loadStrategy}`);
    console.log(`   • Modal style: ${window.SnipcartSettings.modalStyle}`);
  } else {
    console.log('   ❌ Snipcart settings not found');
  }
  
  // Check if Snipcart is loaded
  if (window.Snipcart) {
    console.log('   ✅ Snipcart library loaded');
  } else {
    console.log('   ⚠️  Snipcart library not yet loaded (this is normal with on-user-interaction)');
  }
  
  // Check for Snipcart elements
  const snipcartDiv = document.querySelector('#snipcart');
  if (snipcartDiv) {
    console.log('   ✅ Snipcart container found');
  } else {
    console.log('   ❌ Snipcart container not found');
  }
}

// Test 4: Check Network Requests
function testNetworkRequests() {
  console.log('\n4. Testing Network Performance...');
  
  if (performance && performance.getEntriesByType) {
    const resources = performance.getEntriesByType('resource');
    
    console.log(`   • Total resources loaded: ${resources.length}`);
    
    // Find slow resources (>100ms)
    const slowResources = resources.filter(resource => resource.duration > 100);
    if (slowResources.length > 0) {
      console.log(`   ⚠️  ${slowResources.length} slow resources (>100ms):`);
      slowResources.forEach(resource => {
        console.log(`      - ${resource.name.split('/').pop()} (${Math.round(resource.duration)}ms)`);
      });
    } else {
      console.log('   ✅ All resources loaded quickly');
    }
    
    // Check for failed requests
    const failedResources = resources.filter(resource => 
      resource.transferSize === 0 && resource.decodedBodySize === 0
    );
    if (failedResources.length > 0) {
      console.log(`   ❌ ${failedResources.length} failed requests:`);
      failedResources.forEach(resource => {
        console.log(`      - ${resource.name}`);
      });
    } else {
      console.log('   ✅ No failed requests detected');
    }
  } else {
    console.log('   ⚠️  Performance API not available');
  }
}

// Test 5: Check Page Load Performance
function testPageLoadPerformance() {
  console.log('\n5. Testing Page Load Performance...');
  
  if (performance && performance.timing) {
    const timing = performance.timing;
    const loadTime = timing.loadEventEnd - timing.navigationStart;
    const domContentLoaded = timing.domContentLoadedEventEnd - timing.navigationStart;
    
    console.log(`   • Total load time: ${loadTime}ms`);
    console.log(`   • DOM Content Loaded: ${domContentLoaded}ms`);
    
    if (loadTime < 1000) {
      console.log('   ✅ Excellent load time');
    } else if (loadTime < 3000) {
      console.log('   ⚠️  Good load time');
    } else {
      console.log('   ❌ Slow load time');
    }
  }
}

// Run all tests
function runAllTests() {
  testCSSLoading();
  testJavaScriptLoading();
  testSnipcartIntegration();
  testNetworkRequests();
  testPageLoadPerformance();
  
  console.log('\n🎯 Performance test complete!');
  console.log('💡 To test Snipcart loading, try clicking on the cart button or interacting with the page.');
}

// Auto-run tests when script loads
runAllTests();

// Make functions available globally for manual testing
window.testPerformance = {
  runAll: runAllTests,
  css: testCSSLoading,
  js: testJavaScriptLoading,
  snipcart: testSnipcartIntegration,
  network: testNetworkRequests,
  pageLoad: testPageLoadPerformance
};

console.log('\n📋 Available commands:');
console.log('• testPerformance.runAll() - Run all tests');
console.log('• testPerformance.css() - Test CSS loading');
console.log('• testPerformance.js() - Test JavaScript loading');
console.log('• testPerformance.snipcart() - Test Snipcart integration');
console.log('• testPerformance.network() - Test network requests');
console.log('• testPerformance.pageLoad() - Test page load performance');
