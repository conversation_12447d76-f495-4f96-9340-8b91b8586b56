/**
 * Performance Optimizer - Reduces Total Blocking Time (TBT)
 * Defers non-critical JavaScript and optimizes event listeners
 */

(function() {
  'use strict';
  
  // Performance configuration
  const PERF_CONFIG = {
    deferNonCritical: true,
    optimizeEventListeners: true,
    enableIdleCallback: true,
    maxBlockingTime: 50 // ms
  };
  
  /**
   * Defer non-critical JavaScript execution
   */
  function deferNonCriticalJS() {
    const nonCriticalTasks = [];
    
    // Defer analytics and tracking
    nonCriticalTasks.push(() => {
      // Google Analytics is already deferred in production
      console.log('📊 Analytics deferred');
    });
    
    // Defer non-essential UI enhancements
    nonCriticalTasks.push(() => {
      // Smooth scrolling enhancements
      if ('scrollBehavior' in document.documentElement.style) {
        document.documentElement.style.scrollBehavior = 'smooth';
      }
    });
    
    // Defer image lazy loading setup for non-LCP images
    nonCriticalTasks.push(() => {
      const lazyImages = document.querySelectorAll('img[loading="lazy"]');
      if ('IntersectionObserver' in window && lazyImages.length > 0) {
        const imageObserver = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target;
              if (img.dataset.src) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
              }
              imageObserver.unobserve(img);
            }
          });
        }, { rootMargin: '50px 0px' });
        
        lazyImages.forEach(img => imageObserver.observe(img));
      }
    });
    
    // Execute tasks in idle time or with timeout
    if (PERF_CONFIG.enableIdleCallback && 'requestIdleCallback' in window) {
      nonCriticalTasks.forEach((task, index) => {
        requestIdleCallback(task, { timeout: 1000 + (index * 100) });
      });
    } else {
      // Fallback: execute with staggered timeouts
      nonCriticalTasks.forEach((task, index) => {
        setTimeout(task, 100 + (index * 50));
      });
    }
  }
  
  /**
   * Optimize event listeners for better scrolling performance
   */
  function optimizeEventListeners() {
    // Override addEventListener to automatically add passive where appropriate
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    
    EventTarget.prototype.addEventListener = function(type, listener, options) {
      // Auto-add passive for scroll-related events that don't need preventDefault
      const passiveEvents = ['scroll', 'touchstart', 'touchmove', 'wheel'];
      
      if (passiveEvents.includes(type) && typeof options !== 'object') {
        options = { passive: true };
      } else if (passiveEvents.includes(type) && typeof options === 'object' && options.passive === undefined) {
        options.passive = true;
      }
      
      return originalAddEventListener.call(this, type, listener, options);
    };
  }
  
  /**
   * Break up long tasks to reduce blocking time
   */
  function breakUpLongTasks() {
    // Utility function to yield to main thread
    window.yieldToMain = function() {
      return new Promise(resolve => {
        if ('scheduler' in window && 'postTask' in window.scheduler) {
          window.scheduler.postTask(resolve, { priority: 'user-blocking' });
        } else {
          setTimeout(resolve, 0);
        }
      });
    };
    
    // Utility function for time-sliced execution
    window.timeSlice = async function(tasks, timeSlice = 5) {
      const start = performance.now();
      
      for (const task of tasks) {
        await task();
        
        // Yield if we've been running too long
        if (performance.now() - start > timeSlice) {
          await window.yieldToMain();
          start = performance.now();
        }
      }
    };
  }
  
  /**
   * Monitor and log performance metrics
   */
  function monitorPerformance() {
    if (!('PerformanceObserver' in window)) return;
    
    try {
      // Monitor Long Tasks
      const longTaskObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (entry.duration > PERF_CONFIG.maxBlockingTime) {
            console.warn(`⚠️ Long task detected: ${entry.duration.toFixed(2)}ms`);
          }
        });
      });
      
      if (PerformanceObserver.supportedEntryTypes.includes('longtask')) {
        longTaskObserver.observe({ entryTypes: ['longtask'] });
      }
      
      // Monitor Total Blocking Time (approximation)
      let totalBlockingTime = 0;
      const tbtObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (entry.duration > 50) {
            totalBlockingTime += entry.duration - 50;
          }
        });
        
        if (totalBlockingTime > 0) {
          console.log(`📊 Estimated TBT: ${totalBlockingTime.toFixed(2)}ms`);
        }
      });
      
      if (PerformanceObserver.supportedEntryTypes.includes('longtask')) {
        tbtObserver.observe({ entryTypes: ['longtask'] });
      }
      
    } catch (error) {
      console.warn('Performance monitoring failed:', error);
    }
  }
  
  /**
   * Initialize performance optimizations
   */
  function initPerformanceOptimizer() {
    // Apply optimizations immediately but non-blocking
    if (PERF_CONFIG.optimizeEventListeners) {
      optimizeEventListeners();
    }
    
    // Break up long tasks utility
    breakUpLongTasks();
    
    // Defer non-critical work
    if (PERF_CONFIG.deferNonCritical) {
      if ('requestIdleCallback' in window) {
        requestIdleCallback(deferNonCriticalJS, { timeout: 1000 });
      } else {
        setTimeout(deferNonCriticalJS, 100);
      }
    }
    
    // Start performance monitoring
    if ('requestIdleCallback' in window) {
      requestIdleCallback(monitorPerformance, { timeout: 2000 });
    } else {
      setTimeout(monitorPerformance, 500);
    }
    
    console.log('⚡ Performance optimizer initialized');
  }
  
  /**
   * Expose performance utilities
   */
  window.PerformanceOptimizer = {
    yieldToMain: () => window.yieldToMain(),
    timeSlice: (tasks, timeSlice) => window.timeSlice(tasks, timeSlice),
    config: PERF_CONFIG
  };
  
  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPerformanceOptimizer, { passive: true });
  } else {
    initPerformanceOptimizer();
  }
  
})();
