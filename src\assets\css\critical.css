/* Critical CSS - Above-the-fold styles only */
/* This file contains only the essential styles needed for initial page render */

/* CSS Variables - Essential for all styling */
:root {
  --primary: #92400e;
  --primary-dark: #78350f;
  --primary-light: #d97706;
  --secondary: #1e293b;
  --secondary-light: #334155;
  --background: #f8fafc;
  --light-background: #ffffff;
  --card-bg: #ffffff;
  --text: #0f172a;
  --text-secondary: #475569;
  --muted: #64748b;
  --border: #e2e8f0;
  --border-light: #f1f5f9;
  --radius: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --container-width: 1200px;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
  --font-system: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-serif: "Playfair Display", Georgia, serif;
}

/* Base reset and typography - Critical for layout */
*, *::before, *::after {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: var(--font-system);
  background: var(--background);
  color: var(--text);
  line-height: 1.6;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Container - Critical for layout */
.container {
  max-width: var(--container-width);
  margin: 0 auto;
  padding: 0 2rem;
}

/* Typography - Critical for content hierarchy */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-serif);
  color: var(--text);
  font-weight: 600;
  line-height: 1.2;
  margin: 0 0 1rem 0;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

/* Header - Critical for above-the-fold */
.site-header {
  background: var(--light-background);
  border-bottom: 1px solid var(--border);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  min-height: 70px;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: opacity 0.2s ease;
}

/* Navigation - Critical for usability */
.main-nav {
  display: flex;
  gap: 3rem;
  align-items: center;
  flex: 1;
  justify-content: center;
  margin: 0 2rem;
}

.main-nav a {
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  transition: color 0.2s ease;
  padding: 0.75rem 1rem;
  border-radius: var(--radius);
  white-space: nowrap;
  font-size: 0.95rem;
}

.main-nav a:hover {
  color: var(--primary);
}

/* Buttons - Critical for CTAs */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--radius);
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: 0.875rem;
}

.btn.primary {
  background: var(--primary);
  color: white;
}

.btn.primary:hover {
  background: var(--primary-dark);
}

/* Cart trigger - Critical for e-commerce */
.cart-trigger {
  position: relative;
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  transition: color 0.2s ease;
}

.cart-trigger:hover {
  color: var(--primary);
}

.cart-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background: var(--primary);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

/* Mobile menu - Styles moved to Header.astro component for better specificity */
/* Keeping this comment for reference - actual styles are in Header.astro component */

/* Hamburger line styles moved to Header.astro component for better specificity */

/* Desktop navigation - ensure it's visible */
@media (min-width: 769px) {
  .main-nav {
    display: flex !important;
    gap: 3rem;
    align-items: center;
    flex: 1;
    justify-content: center;
    margin: 0 2rem;
  }
}

/* Critical responsive styles */
@media (max-width: 768px) {
  /* Mobile nav styles moved to Header.astro component to prevent conflicts */

  .container {
    padding: 0 1rem;
  }

  .header-flex {
    padding: 1rem;
  }

  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.25rem; }
  h4 { font-size: 1.125rem; }
}

/* Loading states - Critical for UX */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Accessibility - Critical for compliance */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

*:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Homepage and page-specific critical styles */
.homepage-card {
  background: var(--card-bg);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: 3rem 2.5rem;
  margin: 2rem 0;
  max-width: 640px;
  width: 100%;
  text-align: center;
  border: 1px solid var(--border);
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text);
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Performance optimizations */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
