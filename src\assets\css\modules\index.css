/**
 * CSS Modules Index
 * This file imports all CSS modules for easy inclusion in components
 * Use this when you need multiple modules or import specific modules as needed
 */

/* Import all CSS modules */
@import './buttons.css';
@import './forms.css';
@import './layout.css';

/**
 * Usage Examples:
 * 
 * In Astro components:
 * ---
 * // Import specific modules
 * import buttonStyles from '../assets/css/modules/buttons.css';
 * import formStyles from '../assets/css/modules/forms.css';
 * 
 * // Or import all modules
 * import moduleStyles from '../assets/css/modules/index.css';
 * ---
 * 
 * <button class="btn primary">Click me</button>
 * <form class="form-section">
 *   <div class="form-group">
 *     <label class="form-label">Name</label>
 *     <input class="form-input" type="text" />
 *   </div>
 * </form>
 * 
 * Or use scoped styles in components:
 * <style>
 *   @import '../assets/css/modules/buttons.css';
 *   
 *   .my-custom-button {
 *     @apply btn primary large;
 *   }
 * </style>
 */
