---
// Condition Guide Page
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
---

<Layout
  title="Condition Guide - Cheers Marketplace"
  description="Understand our product condition ratings to know exactly what to expect when purchasing gently used items from Cheers Marketplace."
>
  <Header />

  <main class="page-main">
    <div class="container">

      <div class="conditions-grid">
        <!-- New Condition -->
        <div class="condition-card">
          <div class="condition-header">
            <span class="condition-badge condition-new">New</span>
            <h2>New</h2>
          </div>
          <div class="condition-content">
            <p class="condition-description">
              Items that are brand new, unused, and in original packaging (when applicable). 
              These items show no signs of wear and are in pristine condition.
            </p>
            <ul class="condition-features">
              <li>Never used or worn</li>
              <li>Original tags or packaging (when applicable)</li>
              <li>No visible wear, stains, or defects</li>
              <li>Perfect working condition</li>
            </ul>
          </div>
        </div>

        <!-- Excellent Condition -->
        <div class="condition-card">
          <div class="condition-header">
            <span class="condition-badge condition-excellent">Excellent</span>
            <h2>Excellent</h2>
          </div>
          <div class="condition-content">
            <p class="condition-description">
              Items that have been used very lightly and show minimal to no signs of wear. 
              These items look nearly new and function perfectly.
            </p>
            <ul class="condition-features">
              <li>Minimal signs of use</li>
              <li>No visible stains, tears, or damage</li>
              <li>All original functionality intact</li>
              <li>May show very light wear that doesn't affect appearance</li>
            </ul>
          </div>
        </div>

        <!-- Good Condition -->
        <div class="condition-card">
          <div class="condition-header">
            <span class="condition-badge condition-good">Good</span>
            <h2>Good</h2>
          </div>
          <div class="condition-content">
            <p class="condition-description">
              Items that show normal signs of use but remain in solid, functional condition. 
              Any wear is clearly described in the product details.
            </p>
            <ul class="condition-features">
              <li>Normal signs of previous use</li>
              <li>Minor cosmetic wear that doesn't affect function</li>
              <li>All features work as intended</li>
              <li>Any notable wear is described in product details</li>
            </ul>
          </div>
        </div>

        <!-- Fair Condition -->
        <div class="condition-card">
          <div class="condition-header">
            <span class="condition-badge condition-fair">Fair</span>
            <h2>Fair</h2>
          </div>
          <div class="condition-content">
            <p class="condition-description">
              Items that show noticeable wear but are still functional and usable. 
              All defects and wear are thoroughly documented.
            </p>
            <ul class="condition-features">
              <li>Noticeable signs of wear or use</li>
              <li>May have minor cosmetic issues</li>
              <li>Fully functional despite visible wear</li>
              <li>All defects clearly listed in product description</li>
            </ul>
          </div>
        </div>

        <!-- Poor Condition -->
        <div class="condition-card">
          <div class="condition-header">
            <span class="condition-badge condition-poor">Poor</span>
            <h2>Poor</h2>
          </div>
          <div class="condition-content">
            <p class="condition-description">
              Items that show significant wear or have notable defects but may still have value 
              for specific uses, crafting, or repair projects.
            </p>
            <ul class="condition-features">
              <li>Significant visible wear or damage</li>
              <li>May have functional limitations</li>
              <li>Suitable for crafting, parts, or repair projects</li>
              <li>All issues extensively documented</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="guide-footer">
        <div class="quality-assurance">
          <h3>Our Quality Promise</h3>
          <p>
            As a family-run business in Chico, CA, we personally inspect every item before listing. 
            We believe in complete transparency, so any defects, wear, or issues are clearly described 
            in the product details. If you have any questions about an item's condition, please don't 
            hesitate to contact us.
          </p>
        </div>

        <div class="contact-info">
          <p>
            <strong>Questions about an item's condition?</strong><br>
            We're here to help! Contact us for additional photos or details about any product.
          </p>
        </div>
      </div>
    </div>
  </main>

  <Footer />
</Layout>

<style>




  .conditions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
  }

  .condition-card {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
  }

  .condition-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
  }

  .condition-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .condition-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text);
    margin: 0;
  }

  .condition-badge {
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius);
    text-transform: uppercase;
    letter-spacing: 0.025em;
    border: 1px solid;
    white-space: nowrap;
  }

  .condition-poor {
    background: #fef2f2;
    color: #991b1b;
    border-color: #fca5a5;
  }

  .condition-fair {
    background: #fef3c7;
    color: #92400e;
    border-color: #fcd34d;
  }

  .condition-good {
    background: #f0fdf4;
    color: #166534;
    border-color: #86efac;
  }

  .condition-excellent {
    background: #eff6ff;
    color: #1e40af;
    border-color: #93c5fd;
  }

  .condition-new {
    background: #faf5ff;
    color: #7c2d12;
    border-color: #c4b5fd;
  }

  .condition-description {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }

  .condition-features {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .condition-features li {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-size: 0.9375rem;
    color: var(--text-secondary);
    line-height: 1.5;
  }

  .condition-features li::before {
    content: "✓";
    color: var(--primary);
    font-weight: 600;
    flex-shrink: 0;
    margin-top: 0.125rem;
  }

  .guide-footer {
    background: var(--light-background);
    border-radius: var(--radius-lg);
    padding: 2.5rem;
    text-align: center;
  }

  .quality-assurance {
    margin-bottom: 2rem;
  }

  .quality-assurance h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text);
    margin-bottom: 1rem;
  }

  .quality-assurance p,
  .contact-info p {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
  }

  .contact-info strong {
    color: var(--text);
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .condition-guide-main {
      padding: 1.5rem 0 3rem;
    }

    .guide-header h1 {
      font-size: 2rem;
    }

    .guide-intro {
      font-size: 1rem;
    }

    .conditions-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
      margin-bottom: 3rem;
    }

    .condition-card {
      padding: 1.5rem;
    }

    .condition-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.75rem;
    }

    .guide-footer {
      padding: 2rem;
    }
  }

  @media (max-width: 480px) {
    .condition-card {
      padding: 1.25rem;
    }

    .guide-footer {
      padding: 1.5rem;
    }
  }
</style>
