/**
 * Cache Performance Monitoring
 * Tracks cache hit rates, performance metrics, and optimization opportunities
 */

export interface CacheMetrics {
  hitRate: number;
  missRate: number;
  totalRequests: number;
  cacheHits: number;
  cacheMisses: number;
  averageResponseTime: number;
  storageUsage: {
    used: number;
    quota: number;
    percentage: number;
  };
}

export interface ResourceTiming {
  url: string;
  duration: number;
  transferSize: number;
  encodedBodySize: number;
  decodedBodySize: number;
  cacheHit: boolean;
  resourceType: string;
}

/**
 * Cache performance monitor
 */
export class CacheMonitor {
  private metrics: CacheMetrics;
  private resourceTimings: ResourceTiming[] = [];
  private observer: PerformanceObserver | null = null;

  constructor() {
    this.metrics = {
      hitRate: 0,
      missRate: 0,
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageResponseTime: 0,
      storageUsage: {
        used: 0,
        quota: 0,
        percentage: 0
      }
    };

    this.initializeMonitoring();
  }

  /**
   * Initialize performance monitoring
   */
  private initializeMonitoring(): void {
    if (!('PerformanceObserver' in window)) {
      console.warn('PerformanceObserver not supported');
      return;
    }

    try {
      this.observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation' || entry.entryType === 'resource') {
            this.processResourceTiming(entry as PerformanceResourceTiming);
          }
        });
      });

      this.observer.observe({ entryTypes: ['navigation', 'resource'] });
    } catch (error) {
      console.error('Failed to initialize performance monitoring:', error);
    }
  }

  /**
   * Process resource timing entry
   */
  private processResourceTiming(entry: PerformanceResourceTiming): void {
    const resourceTiming: ResourceTiming = {
      url: entry.name,
      duration: entry.duration,
      transferSize: entry.transferSize || 0,
      encodedBodySize: entry.encodedBodySize || 0,
      decodedBodySize: entry.decodedBodySize || 0,
      cacheHit: entry.transferSize === 0 && entry.decodedBodySize > 0,
      resourceType: this.getResourceType(entry.name)
    };

    this.resourceTimings.push(resourceTiming);
    this.updateMetrics(resourceTiming);
  }

  /**
   * Determine resource type from URL
   */
  private getResourceType(url: string): string {
    if (url.includes('b-cdn.net') || url.includes('bunnycdn.com')) {
      return 'cdn-image';
    }
    if (url.match(/\.(css)$/i)) return 'stylesheet';
    if (url.match(/\.(js)$/i)) return 'script';
    if (url.match(/\.(jpg|jpeg|png|webp|avif|svg)$/i)) return 'image';
    if (url.match(/\.(woff|woff2)$/i)) return 'font';
    if (url.includes('/api/')) return 'api';
    return 'other';
  }

  /**
   * Update cache metrics
   */
  private updateMetrics(timing: ResourceTiming): void {
    this.metrics.totalRequests++;
    
    if (timing.cacheHit) {
      this.metrics.cacheHits++;
    } else {
      this.metrics.cacheMisses++;
    }

    this.metrics.hitRate = (this.metrics.cacheHits / this.metrics.totalRequests) * 100;
    this.metrics.missRate = (this.metrics.cacheMisses / this.metrics.totalRequests) * 100;

    // Update average response time
    const totalDuration = this.resourceTimings.reduce((sum, t) => sum + t.duration, 0);
    this.metrics.averageResponseTime = totalDuration / this.resourceTimings.length;
  }

  /**
   * Get current cache metrics
   */
  public async getMetrics(): Promise<CacheMetrics> {
    // Update storage usage
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      try {
        const estimate = await navigator.storage.estimate();
        this.metrics.storageUsage = {
          used: estimate.usage || 0,
          quota: estimate.quota || 0,
          percentage: estimate.quota ? ((estimate.usage || 0) / estimate.quota) * 100 : 0
        };
      } catch (error) {
        console.error('Failed to get storage estimate:', error);
      }
    }

    return { ...this.metrics };
  }

  /**
   * Get resource timings by type
   */
  public getResourceTimingsByType(type: string): ResourceTiming[] {
    return this.resourceTimings.filter(timing => timing.resourceType === type);
  }

  /**
   * Get CDN cache performance
   */
  public getCDNPerformance(): {
    totalCDNRequests: number;
    cdnCacheHits: number;
    cdnHitRate: number;
    averageCDNResponseTime: number;
    totalCDNTransferSize: number;
  } {
    const cdnTimings = this.getResourceTimingsByType('cdn-image');
    const totalCDNRequests = cdnTimings.length;
    const cdnCacheHits = cdnTimings.filter(t => t.cacheHit).length;
    const cdnHitRate = totalCDNRequests > 0 ? (cdnCacheHits / totalCDNRequests) * 100 : 0;
    const averageCDNResponseTime = totalCDNRequests > 0 
      ? cdnTimings.reduce((sum, t) => sum + t.duration, 0) / totalCDNRequests 
      : 0;
    const totalCDNTransferSize = cdnTimings.reduce((sum, t) => sum + t.transferSize, 0);

    return {
      totalCDNRequests,
      cdnCacheHits,
      cdnHitRate,
      averageCDNResponseTime,
      totalCDNTransferSize
    };
  }

  /**
   * Generate performance report
   */
  public async generateReport(): Promise<string> {
    const metrics = await this.getMetrics();
    const cdnPerf = this.getCDNPerformance();

    return `
Cache Performance Report
========================

Overall Cache Performance:
- Hit Rate: ${metrics.hitRate.toFixed(2)}%
- Miss Rate: ${metrics.missRate.toFixed(2)}%
- Total Requests: ${metrics.totalRequests}
- Average Response Time: ${metrics.averageResponseTime.toFixed(2)}ms

CDN Performance:
- CDN Requests: ${cdnPerf.totalCDNRequests}
- CDN Hit Rate: ${cdnPerf.cdnHitRate.toFixed(2)}%
- CDN Response Time: ${cdnPerf.averageCDNResponseTime.toFixed(2)}ms
- CDN Transfer Size: ${(cdnPerf.totalCDNTransferSize / 1024).toFixed(2)} KB

Storage Usage:
- Used: ${(metrics.storageUsage.used / 1024 / 1024).toFixed(2)} MB
- Quota: ${(metrics.storageUsage.quota / 1024 / 1024).toFixed(2)} MB
- Percentage: ${metrics.storageUsage.percentage.toFixed(2)}%

Resource Breakdown:
${this.getResourceBreakdown()}
    `.trim();
  }

  /**
   * Get resource breakdown
   */
  private getResourceBreakdown(): string {
    const typeGroups = this.resourceTimings.reduce((groups, timing) => {
      if (!groups[timing.resourceType]) {
        groups[timing.resourceType] = [];
      }
      groups[timing.resourceType].push(timing);
      return groups;
    }, {} as Record<string, ResourceTiming[]>);

    return Object.entries(typeGroups)
      .map(([type, timings]) => {
        const count = timings.length;
        const hits = timings.filter(t => t.cacheHit).length;
        const hitRate = count > 0 ? (hits / count) * 100 : 0;
        const avgTime = count > 0 ? timings.reduce((sum, t) => sum + t.duration, 0) / count : 0;
        
        return `- ${type}: ${count} requests, ${hitRate.toFixed(1)}% hit rate, ${avgTime.toFixed(1)}ms avg`;
      })
      .join('\n');
  }

  /**
   * Log performance summary to console
   */
  public async logSummary(): Promise<void> {
    const report = await this.generateReport();
    console.log(report);
  }

  /**
   * Disconnect the observer
   */
  public disconnect(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  }
}

// Global cache monitor instance
let globalCacheMonitor: CacheMonitor | null = null;

/**
 * Get or create global cache monitor
 */
export function getCacheMonitor(): CacheMonitor {
  if (!globalCacheMonitor) {
    globalCacheMonitor = new CacheMonitor();
  }
  return globalCacheMonitor;
}

/**
 * Initialize cache monitoring (call once on page load)
 */
export function initializeCacheMonitoring(): CacheMonitor {
  const monitor = getCacheMonitor();
  
  // Log summary after 10 seconds
  setTimeout(() => {
    monitor.logSummary();
  }, 10000);

  return monitor;
}

// Auto-initialize in development
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  document.addEventListener('DOMContentLoaded', () => {
    initializeCacheMonitoring();
  });
}
