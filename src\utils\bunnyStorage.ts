/**
 * Bunny Storage API Utilities
 * Handles file uploads to Bunny CDN storage
 */

export interface BunnyStorageConfig {
  storageZoneName: string;
  apiKey: string;
  region: string;
  baseUrl: string;
}

export interface UploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

/**
 * Get Bunny Storage configuration from environment variables
 */
export function getBunnyStorageConfig(): BunnyStorageConfig {
  const storageZoneName = import.meta.env.BUNNY_STORAGE_ZONE_NAME;
  const apiKey = import.meta.env.BUNNY_STORAGE_API_KEY;
  const region = import.meta.env.BUNNY_STORAGE_REGION;
  const baseUrl = import.meta.env.BUNNY_STORAGE_BASE_URL;

  if (!storageZoneName || !apiKey || !region || !baseUrl) {
    throw new Error('Missing required Bunny Storage configuration. Please check your environment variables.');
  }

  return {
    storageZoneName,
    apiKey,
    region,
    baseUrl
  };
}

/**
 * Get the storage endpoint URL based on region
 */
export function getStorageEndpoint(region: string): string {
  const endpoints: Record<string, string> = {
    'de': 'storage.bunnycdn.com',
    'uk': 'uk.storage.bunnycdn.com',
    'ny': 'ny.storage.bunnycdn.com',
    'la': 'la.storage.bunnycdn.com',
    'sg': 'sg.storage.bunnycdn.com',
    'se': 'se.storage.bunnycdn.com',
    'br': 'br.storage.bunnycdn.com',
    'jh': 'jh.storage.bunnycdn.com',
    'syd': 'syd.storage.bunnycdn.com'
  };

  return endpoints[region.toLowerCase()] || 'storage.bunnycdn.com';
}

/**
 * Generate a unique filename with timestamp and random suffix
 */
export function generateUniqueFilename(originalName: string): string {
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg';
  const baseName = originalName.split('.')[0].replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
  
  return `${baseName}-${timestamp}-${randomSuffix}.${extension}`;
}

/**
 * Validate image file
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.'
    };
  }

  // Check file size (10MB limit)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'File size too large. Maximum size is 10MB.'
    };
  }

  return { valid: true };
}

/**
 * Upload file to Bunny Storage
 */
export async function uploadToBunnyStorage(
  file: File,
  path: string = 'products'
): Promise<UploadResult> {
  try {
    const config = getBunnyStorageConfig();
    const validation = validateImageFile(file);
    
    if (!validation.valid) {
      return {
        success: false,
        error: validation.error
      };
    }

    const filename = generateUniqueFilename(file.name);
    const storageEndpoint = getStorageEndpoint(config.region);
    const uploadUrl = `https://${storageEndpoint}/${config.storageZoneName}/${path}/${filename}`;

    // Convert file to ArrayBuffer for upload
    const fileBuffer = await file.arrayBuffer();

    const response = await fetch(uploadUrl, {
      method: 'PUT',
      headers: {
        'AccessKey': config.apiKey,
        'Content-Type': 'application/octet-stream'
      },
      body: fileBuffer
    });

    if (!response.ok) {
      const errorText = await response.text();
      return {
        success: false,
        error: `Upload failed: ${response.status} ${response.statusText}. ${errorText}`
      };
    }

    // Generate the CDN URL
    const cdnUrl = `${config.baseUrl}/${path}/${filename}`;

    return {
      success: true,
      url: cdnUrl
    };

  } catch (error) {
    console.error('Bunny Storage upload error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown upload error'
    };
  }
}

/**
 * Upload multiple files to Bunny Storage
 */
export async function uploadMultipleFiles(
  files: File[],
  path: string = 'products'
): Promise<UploadResult[]> {
  const uploadPromises = files.map(file => uploadToBunnyStorage(file, path));
  return Promise.all(uploadPromises);
}
