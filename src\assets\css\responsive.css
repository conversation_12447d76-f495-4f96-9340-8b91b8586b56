/* Responsive Design and Mobile Optimizations */

/* Performance Optimizations */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border: #000000;
    --text-secondary: #000000;
    --shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
  }
  
  .product-card,
  .modal-content,
  .admin-card {
    border-width: 2px;
  }
}

/* Print styles */
@media print {
  * {
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
    font-size: 12pt;
    line-height: 1.4;
  }
  
  .site-header,
  .modal-overlay,
  .admin-nav,
  .admin-actions,
  .btn,
  button,
  .cta-btn {
    display: none !important;
  }
  
  .admin-container,
  .products-container {
    max-width: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }
  
  .product-card {
    break-inside: avoid;
    margin-bottom: 1rem;
    border: 1px solid #000;
  }
  
  .admin-table {
    font-size: 10pt;
  }
  
  .admin-table th,
  .admin-table td {
    padding: 0.25rem;
    border: 1px solid #000;
  }
  
  a::after {
    content: " (" attr(href) ")";
    font-size: 0.8em;
    color: #666;
  }
  
  .product-price::before {
    content: "Price: ";
  }
}

/* Mobile-first responsive breakpoints */

/* Small mobile devices (320px and up) */
@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }
  
  /* Typography adjustments */
  h1 {
    font-size: 2rem;
    line-height: 1.2;
  }
  
  h2 {
    font-size: 1.5rem;
    line-height: 1.3;
  }
  
  h3 {
    font-size: 1.25rem;
    line-height: 1.3;
  }
  
  /* Button adjustments */
  .btn,
  .cta-btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }
  
  .btn.small {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }
  
  /* Modal adjustments */
  .modal-content {
    max-width: 95vw;
    max-height: 95vh;
    margin: 0.5rem;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }
  
  .modal-footer {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .modal-footer .btn {
    width: 100%;
    justify-content: center;
  }
  
  /* Form adjustments */
  .form-input,
  .form-textarea,
  .form-select {
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  /* Alert adjustments */
  .alert {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
  
  /* Homepage card */
  .homepage-card {
    padding: 2rem 1.5rem;
    margin: 1rem 0;
  }
}

/* Large mobile devices (481px to 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .container {
    padding: 0 1.5rem;
  }
  
  /* Modal adjustments */
  .modal-content {
    max-width: 90vw;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1.5rem;
  }
  
  /* Button groups */
  .admin-actions,
  .modal-footer {
    flex-wrap: wrap;
  }
  
  .admin-actions .btn,
  .modal-footer .btn {
    flex: 1;
    min-width: 120px;
  }
}

/* Tablet devices (769px to 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .container {
    padding: 0 2rem;
  }
  
  /* Grid adjustments */
  .admin-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  /* Modal adjustments */
  .modal-content {
    max-width: 80vw;
  }
}

/* Large screens (1025px to 1440px) */
@media (min-width: 1025px) and (max-width: 1440px) {
  .container {
    max-width: 1200px;
  }
  
  /* Optimize for larger screens */
  .admin-stats {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Extra large screens (1441px and up) */
@media (min-width: 1441px) {
  .container {
    max-width: 1400px;
  }
  
  /* Scale up for very large screens */
  .admin-stats {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .modal-content {
    max-width: 60vw;
  }
}

/* Landscape orientation optimizations */
@media (orientation: landscape) and (max-height: 600px) {
  .modal-content {
    max-height: 85vh;
  }
  
  .modal-body {
    max-height: calc(85vh - 120px);
  }
  
  .homepage-card {
    padding: 1.5rem 2rem;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Increase touch targets */
  .btn,
  .admin-table-btn,
  .modal-close {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Remove hover effects on touch devices */
  .product-card:hover,
  .btn:hover,
  .admin-nav-item:hover {
    transform: none;
    box-shadow: var(--shadow);
  }
  
  /* Optimize for touch scrolling */
  .modal-body,
  .admin-table-wrapper {
    -webkit-overflow-scrolling: touch;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Optimize for retina displays */
  .product-image,
  .product-image-thumb {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark mode support (if implemented) */
@media (prefers-color-scheme: dark) {
  /* This would be implemented if dark mode is added */
  /*
  :root {
    --background: #0f172a;
    --light-background: #1e293b;
    --card-bg: #1e293b;
    --text: #f8fafc;
    --text-secondary: #cbd5e1;
    --border: #334155;
    --border-light: #475569;
  }
  */
}

/* Focus management for keyboard navigation */
@media (prefers-reduced-motion: no-preference) {
  :focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
    transition: outline-offset 0.2s ease;
  }
}

/* Specific component responsive overrides */

/* Admin panel mobile optimizations */
@media (max-width: 768px) {
  .admin-container {
    padding: 1rem;
  }
  
  .admin-header {
    padding: 1.5rem;
    text-align: center;
  }
  
  .admin-nav {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .admin-nav-item {
    justify-content: center;
    text-align: center;
  }
  
  .admin-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .admin-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .admin-btn {
    justify-content: center;
  }
  
  .categories-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .admin-table-wrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .admin-table {
    min-width: 600px;
  }
  
  .admin-table th,
  .admin-table td {
    padding: 0.5rem;
    font-size: 0.75rem;
  }
  
  .admin-table-actions {
    flex-direction: column;
    gap: 0.25rem;
    min-width: 80px;
  }
  
  .admin-table-btn {
    font-size: 0.625rem;
    padding: 0.25rem 0.5rem;
    width: 100%;
    justify-content: center;
  }
  
  .product-image-thumb {
    width: 40px;
    height: 30px;
  }
}
