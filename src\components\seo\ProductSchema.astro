---
// Product Schema Component
// Generates product structured data for individual product pages

import StructuredData from './StructuredData.astro';
import { generateProductSchema } from '../../utils/structuredData.ts';

export interface Props {
  product: {
    id: string;
    name: string;
    description: string;
    price: number;
    category: string;
    firstImage?: string;
    images?: string[];
    slug: string;
    brand?: string;
    sku?: string;
    condition?: 'NewCondition' | 'UsedCondition' | 'RefurbishedCondition';
    availability?: 'InStock' | 'OutOfStock' | 'PreOrder';
  };
  baseUrl?: string;
}

const { 
  product, 
  baseUrl = "https://www.cheersmarketplace.com" 
} = Astro.props;

// Prepare product data for schema
const productUrl = `${baseUrl}/products/${product.slug}/`;
const productImages = product.images || (product.firstImage ? [product.firstImage] : []);

// Ensure we have at least one image (use placeholder if needed)
const images = productImages.length > 0 
  ? productImages 
  : [`${baseUrl}/images/product-placeholder.svg`];

const productData = {
  id: product.id,
  name: product.name,
  description: product.description,
  image: images,
  price: product.price,
  currency: "USD",
  availability: product.availability || 'InStock',
  condition: product.condition || 'NewCondition',
  brand: product.brand || "Cheers Marketplace",
  category: product.category,
  sku: product.sku || product.id,
  url: productUrl,
  offers: {
    price: product.price,
    currency: "USD",
    availability: `https://schema.org/${product.availability || 'InStock'}`,
    validFrom: new Date().toISOString().split('T')[0], // Today's date
  }
};

const productSchema = generateProductSchema(productData);
---

<StructuredData schema={productSchema} id="product-schema" />

<!-- 
This component should be included in product page templates
to provide rich product information for search engines.

Usage in product pages:
<ProductSchema product={product} />
<ProductSchema product={product} baseUrl="https://custom-domain.com" />
-->
