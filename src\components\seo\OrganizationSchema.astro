---
// Organization Schema Component
// Generates organization structured data for Cheers Marketplace

import StructuredData from './StructuredData.astro';
import { generateOrganizationSchema, generateWebSiteSchema, combineSchemas } from '../../utils/structuredData.ts';

export interface Props {
  includeWebSite?: boolean;
}

const { includeWebSite = true } = Astro.props;

// Cheers Marketplace organization data
const organizationData = {
  name: "Cheers Marketplace",
  url: "https://www.cheersmarketplace.com",
  logo: "https://www.cheersmarketplace.com/logo.png",
  description: "Discover unique, curated products from passionate creators. We connect you with the best local and global makers, all in one warm, welcoming place.",
  address: {
    streetAddress: "123 Main Street",
    addressLocality: "Chico",
    addressRegion: "CA",
    postalCode: "95928",
    addressCountry: "US"
  },
  contactPoint: {
    telephone: "******-555-0123",
    contactType: "customer service",
    email: "<EMAIL>"
  },
  sameAs: [
    "https://www.facebook.com/cheersmarketplace",
    "https://www.instagram.com/cheersmarketplace",
    "https://twitter.com/cheersmarket"
  ]
};

// Generate schemas
const organizationSchema = generateOrganizationSchema(organizationData);

const schemas = [organizationSchema];

if (includeWebSite) {
  const webSiteSchema = generateWebSiteSchema(
    organizationData.name,
    organizationData.url,
    organizationData.description
  );
  schemas.push(webSiteSchema);
}
---

<StructuredData schema={schemas} id="organization-schema" />

<!-- 
This component should be included in the Layout.astro head section
to provide consistent organization data across all pages.

Usage:
<OrganizationSchema />
<OrganizationSchema includeWebSite={false} />
-->
