/* Prevent layout shift from scrollbar appearing on long pages */
html {
  overflow-y: scroll;
}
/* Optimized Global CSS - Critical Path Only */
/* Non-critical styles are loaded asynchronously via Layout.astro */

/* Import only critical CSS modules for above-the-fold content */
@import './css/variables.css';
@import './css/base.css';
@import './css/header.css';

/* Homepage specific styles */
.homepage-card {
  background: var(--card-bg);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: 3rem 2.5rem;
  margin: 2rem auto;
  max-width: 640px;
  width: 100%;
  text-align: center;
  border: 1px solid var(--border);
}

/* Standardized Page Layout System */
.page-main {
  padding-top: 0;
  min-height: calc(100vh - 72px);
}

/* Standardized Page Title System */
.page-hero {
  background: linear-gradient(135deg, var(--light-background) 0%, var(--border-light) 100%);
  border-bottom: 1px solid var(--border);
  padding: 2rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
  margin-top: 0;
}

.page-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(146, 64, 14, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.page-hero h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text);
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  position: relative;
  line-height: 1.2;
}

.page-hero .hero-description {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  position: relative;
}

/* Mobile responsive for page titles */
@media (max-width: 768px) {
  .page-hero {
    padding: 1.5rem 0;
  }

  .page-hero h1 {
    font-size: 2rem;
  }

  .page-hero .hero-description {
    font-size: 1rem;
  }
}

/* Legacy page-specific styles for backward compatibility */
.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text);
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}
