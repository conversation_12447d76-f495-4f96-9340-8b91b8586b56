---
export const prerender = true;

import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import '../assets/global.css';
---

<Layout
  title="Contact Us - Cheers Marketplace"
  description="Get in touch with Cheers Marketplace. Send us a message about products, orders, or any questions you may have."
>
  <Header />

  <main class="page-main">
    <!-- Contact Content -->
    <section class="contact-content">
      <div class="container">
        <div class="contact-grid">
          
          <!-- Contact Form -->
          <div class="contact-form-section">
            <h2>Send us a Message</h2>
            <form id="contact-form" class="contact-form" action="/api/contact" method="POST">
              
              <div class="form-row two-columns">
                <div class="form-group">
                  <label for="firstName" class="form-label required">First Name</label>
                  <input 
                    type="text" 
                    id="firstName" 
                    name="firstName" 
                    class="form-input" 
                    required 
                    autocomplete="given-name"
                  />
                </div>
                
                <div class="form-group">
                  <label for="lastName" class="form-label required">Last Name</label>
                  <input 
                    type="text" 
                    id="lastName" 
                    name="lastName" 
                    class="form-input" 
                    required 
                    autocomplete="family-name"
                  />
                </div>
              </div>

              <div class="form-group">
                <label for="email" class="form-label required">Email Address</label>
                <input 
                  type="email" 
                  id="email" 
                  name="email" 
                  class="form-input" 
                  required 
                  autocomplete="email"
                />
              </div>

              <div class="form-group">
                <label for="phone" class="form-label">Phone Number</label>
                <input 
                  type="tel" 
                  id="phone" 
                  name="phone" 
                  class="form-input" 
                  autocomplete="tel"
                />
                <div class="form-help">Optional - we'll only call if needed</div>
              </div>

              <div class="form-group">
                <label for="subject" class="form-label required">Subject</label>
                <select id="subject" name="subject" class="form-select" required>
                  <option value="">Please select a topic</option>
                  <option value="product-inquiry">Product Inquiry</option>
                  <option value="order-question">Order Question</option>
                  <option value="shipping">Shipping & Delivery</option>
                  <option value="returns">Returns & Exchanges</option>
                  <option value="general">General Question</option>
                  <option value="feedback">Feedback</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div class="form-group">
                <label for="message" class="form-label required">Message</label>
                <textarea 
                  id="message" 
                  name="message" 
                  class="form-textarea" 
                  rows="6" 
                  required 
                  placeholder="Please provide details about your inquiry..."
                ></textarea>
                <div class="form-help">Minimum 10 characters</div>
              </div>



              <div class="form-actions">
                <button type="submit" class="btn primary" id="submit-btn">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,6.89 21.1,6 20,4Z"/>
                  </svg>
                  Send Message
                </button>
              </div>

              <!-- Form Status Messages -->
              <div id="form-status" class="form-status" style="display: none;"></div>
            </form>
          </div>

          <!-- Contact Information -->
          <div class="contact-info-section">
            <h2>Get in Touch</h2>
            
            <div class="contact-info-card">
              <div class="contact-info-item">
                <div class="contact-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,6.89 21.1,6 20,4Z"/>
                  </svg>
                </div>
                <div class="contact-details">
                  <h3>Email Us</h3>
                  <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                  <p class="contact-note">We typically respond within 24 hours</p>
                </div>
              </div>

              <div class="contact-info-item">
                <div class="contact-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,11.5A2.5,2.5 0 0,1 9.5,9A2.5,2.5 0 0,1 12,6.5A2.5,2.5 0 0,1 14.5,9A2.5,2.5 0 0,1 12,11.5M12,2A7,7 0 0,0 5,9C5,14.25 12,22 12,22C12,22 19,14.25 19,9A7,7 0 0,0 12,2Z"/>
                  </svg>
                </div>
                <div class="contact-details">
                  <h3>Location</h3>
                  <p>Chico, California</p>
                  <p class="contact-note">Family-run business serving the local community</p>
                </div>
              </div>

              <div class="contact-info-item">
                <div class="contact-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.9L16.2,16.2Z"/>
                  </svg>
                </div>
                <div class="contact-details">
                  <h3>Response Time</h3>
                  <p>Within 24 hours</p>
                  <p class="contact-note">Monday - Friday, business hours</p>
                </div>
              </div>
            </div>

            <div class="contact-faq-link">
              <h3>Quick Answers</h3>
              <p>Looking for immediate answers? Check our <a href="/faq/">FAQ page</a> for common questions about orders, shipping, and returns.</p>
            </div>
          </div>

        </div>
      </div>
    </section>
  </main>

  <Footer />
</Layout>

<style>




  .contact-content {
    padding: 5rem 0;
    background: var(--background);
  }

  .contact-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 5rem;
    align-items: start;
  }

  .contact-form-section h2,
  .contact-info-section h2 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text);
    margin-bottom: 2rem;
    letter-spacing: -0.025em;
  }

  .contact-form {
    background: var(--card-bg);
    padding: 3rem;
    border-radius: var(--radius-xl);
    border: 1px solid var(--border);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
  }

  .contact-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
  }

  /* Enhanced Form Styling */
  .form-group {
    margin-bottom: 1.75rem;
  }

  .form-label {
    display: block;
    font-weight: 600;
    color: var(--text);
    font-size: 0.95rem;
    margin-bottom: 0.75rem;
    letter-spacing: -0.01em;
  }

  .form-label.required::after {
    content: ' *';
    color: var(--primary);
    font-weight: 700;
  }

  .form-input,
  .form-select,
  .form-textarea {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid var(--border);
    border-radius: var(--radius-lg);
    background: var(--light-background);
    color: var(--text);
    font-size: 1rem;
    font-family: inherit;
    transition: all 0.3s ease;
    box-sizing: border-box;
  }

  .form-input:focus,
  .form-select:focus,
  .form-textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 4px rgba(146, 64, 14, 0.1);
    background: var(--card-bg);
    transform: translateY(-1px);
  }

  .form-input:hover,
  .form-select:hover,
  .form-textarea:hover {
    border-color: var(--primary-light);
  }

  .form-textarea {
    min-height: 140px;
    resize: vertical;
    line-height: 1.6;
  }

  .form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23146064' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 1rem center;
    background-repeat: no-repeat;
    background-size: 1.25em 1.25em;
    padding-right: 3rem;
    appearance: none;
    cursor: pointer;
  }

  .form-row.two-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }

  .form-help {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: var(--muted);
    line-height: 1.4;
  }



  .form-actions {
    margin-top: 2.5rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-light);
  }

  .btn.primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: white;
    border: none;
    padding: 1rem 2.5rem;
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    box-shadow: var(--shadow-md);
    letter-spacing: -0.01em;
  }

  .btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
  }

  .btn.primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow);
  }

  .btn.primary:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }

  .form-status {
    margin-top: 1.5rem;
    padding: 1.25rem 1.5rem;
    border-radius: var(--radius-lg);
    font-weight: 500;
    font-size: 0.95rem;
    line-height: 1.5;
  }

  .form-status.success {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    color: #15803d;
    border: 1px solid #22c55e;
  }

  .form-status.error {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    color: #dc2626;
    border: 1px solid #ef4444;
  }

  .contact-info-card {
    background: var(--card-bg);
    padding: 2.5rem;
    border-radius: var(--radius-xl);
    border: 1px solid var(--border);
    box-shadow: var(--shadow-lg);
    margin-bottom: 2.5rem;
    position: relative;
    overflow: hidden;
  }

  .contact-info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
  }

  .contact-info-item {
    display: flex;
    gap: 1.25rem;
    margin-bottom: 2.5rem;
    padding: 1.5rem;
    background: var(--background);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
  }

  .contact-info-item:last-child {
    margin-bottom: 0;
  }

  .contact-info-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--border);
  }

  .contact-icon {
    flex-shrink: 0;
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: white;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
  }

  .contact-info-item:hover .contact-icon {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
  }

  .contact-details h3 {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text);
    margin: 0 0 0.75rem 0;
    letter-spacing: -0.01em;
  }

  .contact-details p {
    margin: 0 0 0.5rem 0;
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 0.95rem;
  }

  .contact-details p:last-child {
    margin-bottom: 0;
  }

  .contact-details a {
    color: var(--primary);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;
  }

  .contact-details a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
  }

  .contact-note {
    font-size: 0.875rem;
    color: var(--muted) !important;
    font-style: italic;
  }

  .contact-faq-link {
    background: linear-gradient(135deg, var(--background) 0%, var(--light-background) 100%);
    padding: 2rem;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
  }

  .contact-faq-link:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  .contact-faq-link h3 {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text);
    margin: 0 0 0.75rem 0;
    letter-spacing: -0.01em;
  }

  .contact-faq-link p {
    margin: 0;
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 0.95rem;
  }

  .contact-faq-link a {
    color: var(--primary);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;
  }

  .contact-faq-link a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
  }

  /* Animation for loading spinner */
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .contact-grid {
      gap: 3rem;
    }

    .contact-form {
      padding: 2.5rem;
    }
  }

  @media (max-width: 768px) {

    .contact-content {
      padding: 3rem 0;
    }

    .contact-grid {
      grid-template-columns: 1fr;
      gap: 2.5rem;
    }

    .contact-form {
      padding: 2rem;
    }

    .contact-info-card {
      padding: 2rem;
    }

    .contact-info-item {
      padding: 1.25rem;
    }

    .contact-icon {
      width: 48px;
      height: 48px;
    }

    .form-row.two-columns {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }

  @media (max-width: 480px) {

    .contact-content {
      padding: 2rem 0;
    }

    .contact-form {
      padding: 1.5rem;
    }

    .contact-info-card {
      padding: 1.5rem;
    }

    .contact-info-item {
      flex-direction: column;
      text-align: center;
      padding: 1.5rem;
    }

    .contact-icon {
      align-self: center;
    }

    .btn.primary {
      width: 100%;
      justify-content: center;
      padding: 1.25rem 2rem;
    }

    .form-actions {
      margin-top: 2rem;
      padding-top: 1.5rem;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('contact-form') as HTMLFormElement;
    const submitBtn = document.getElementById('submit-btn') as HTMLButtonElement;
    const statusDiv = document.getElementById('form-status') as HTMLDivElement;

    if (form && submitBtn && statusDiv) {
      form.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Disable submit button and show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" class="animate-spin">
            <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z"/>
          </svg>
          Sending...
        `;

        try {
          const formData = new FormData(form);

          const response = await fetch('/api/contact', {
            method: 'POST',
            body: formData
          });

          const result = await response.json();

          if (response.ok && result.success) {
            statusDiv.className = 'form-status success';
            statusDiv.textContent = 'Thank you! Your message has been sent successfully. We\'ll get back to you within 24 hours.';
            statusDiv.style.display = 'block';
            form.reset();
          } else {
            throw new Error(result.error || 'Failed to send message');
          }
        } catch (error) {
          statusDiv.className = 'form-status error';
          statusDiv.textContent = 'Sorry, there was an error sending your message. Please try again or email us <NAME_EMAIL>.';
          statusDiv.style.display = 'block';
        } finally {
          // Reset submit button
          submitBtn.disabled = false;
          submitBtn.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,6.89 21.1,6 20,4Z"/>
            </svg>
            Send Message
          `;
        }
      });
    }
  });
</script>
