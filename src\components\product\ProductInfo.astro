---
// Product Info Component
export interface Props {
  name: string;
  category: string;
  condition: string;
  shortDescription?: string;
  description: string;
  displayPrice: string;
  keyPoints?: Array<{label: string; value: string}>;
}

const {
  name,
  category,
  condition,
  shortDescription,
  description,
  displayPrice,
  keyPoints
} = Astro.props;
---

<div class="product-info">
  <div class="product-header">
    <h3 class="product-name">{name}</h3>
    <span class="product-category">{category}</span>
  </div>

  <div class="condition-line">
    <span class="condition-label">Condition:</span>
    <span class={`condition-badge condition-${condition.toLowerCase()}`}>{condition}</span>
  </div>

  {shortDescription && (
    <p class="product-description">
      {shortDescription}{description.length > 100 ? '...' : ''}
    </p>
  )}

  <div class="product-footer">
    <div class="product-price">
      <span class="price-amount">{displayPrice}</span>
    </div>

    {keyPoints && keyPoints.length > 0 && (
      <div class="product-features">
        {keyPoints.slice(0, 2).map((kp) => (
          <span class="feature-badge" title={`${kp.label}: ${kp.value}`}>
            {kp.value}
          </span>
        ))}
        {keyPoints.length > 2 && (
          <span class="feature-more">+{keyPoints.length - 2}</span>
        )}
      </div>
    )}
  </div>
</div>

<style>
  .product-info {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    flex: 1;
  }

  .product-header {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
    margin-bottom: 0.75rem;
  }

  .condition-line {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.625rem;
    background: var(--border-light);
    border-radius: var(--radius);
    border: 1px solid var(--border);
    width: fit-content;
    margin-bottom: 0.75rem;
  }

  .condition-label {
    font-size: 0.6875rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .product-name {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text);
    margin: 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
  }

  .product-category {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.05em;
    opacity: 0.8;
  }

  .condition-badge {
    font-size: 0.6875rem;
    font-weight: 700;
    padding: 0.1875rem 0.5rem;
    border-radius: var(--radius);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 1px solid;
  }

  .condition-poor {
    background: #fef2f2;
    color: #991b1b;
    border-color: #fca5a5;
  }

  .condition-fair {
    background: #fef3c7;
    color: #92400e;
    border-color: #fcd34d;
  }

  .condition-good {
    background: #f0fdf4;
    color: #166534;
    border-color: #86efac;
  }

  .condition-excellent {
    background: #eff6ff;
    color: #1e40af;
    border-color: #93c5fd;
  }

  .condition-new {
    background: #faf5ff;
    color: #7c2d12;
    border-color: #c4b5fd;
  }

  .product-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
  }

  .product-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    margin-top: auto;
    padding-top: 0.75rem;
  }

  .product-price {
    display: flex;
    align-items: center;
  }

  .price-amount {
    font-size: 1.375rem;
    font-weight: 800;
    color: var(--primary);
  }

  .product-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.375rem;
    align-items: center;
    justify-content: flex-end;
  }

  .feature-badge {
    background: var(--light-background);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius);
    font-size: 0.6875rem;
    font-weight: 500;
    border: 1px solid var(--border);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80px;
  }

  .feature-more {
    background: var(--border-light);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius);
    font-size: 0.6875rem;
    font-weight: 600;
    border: 1px solid var(--border);
    white-space: nowrap;
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .product-info {
      padding: 0.75rem;
      gap: 0.5rem;
    }

    .product-header {
      margin-bottom: 0.5rem;
    }

    .condition-line {
      padding: 0.1875rem 0.5rem;
      margin-bottom: 0.5rem;
    }

    .condition-label {
      font-size: 0.625rem;
    }

    .product-name {
      font-size: 1rem;
    }

    .product-category {
      font-size: 0.6875rem;
    }

    .product-description {
      font-size: 0.8125rem;
    }

    .price-amount {
      font-size: 1.25rem;
    }

    .product-footer {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
      padding-top: 0.5rem;
    }

    .product-features {
      justify-content: flex-start;
      width: 100%;
    }

    .feature-badge {
      font-size: 0.625rem;
      padding: 0.1875rem 0.375rem;
      max-width: 70px;
    }
  }

  @media (max-width: 480px) {
    .product-info {
      padding: 0.625rem;
      gap: 0.375rem;
    }

    .product-name {
      font-size: 0.9375rem;
    }

    .price-amount {
      font-size: 1.125rem;
    }

    .feature-badge {
      font-size: 0.5625rem;
      padding: 0.125rem 0.25rem;
      max-width: 60px;
    }
  }
</style>
