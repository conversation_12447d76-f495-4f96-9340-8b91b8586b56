---
// Breadcrumb Wrapper Component
// Provides consistent breadcrumb placement across all pages

import AutoBreadcrumbs from './AutoBreadcrumbs.astro';

export interface Props {
  pageTitle?: string;
  productName?: string;
  productSlug?: string;
  productCategory?: string;
  showBreadcrumbs?: boolean;
}

const { 
  pageTitle, 
  productName, 
  productSlug, 
  productCategory, 
  showBreadcrumbs = true 
} = Astro.props;

// Don't show breadcrumbs on home page
const isHomePage = Astro.url.pathname === '/';
const shouldShowBreadcrumbs = showBreadcrumbs && !isHomePage;
---

{shouldShowBreadcrumbs && (
  <div class="breadcrumb-section">
    <div class="container">
      <div class="breadcrumb-container">
        {productName ? (
          <AutoBreadcrumbs 
            productName={productName}
            productSlug={productSlug}
            productCategory={productCategory}
          />
        ) : pageTitle ? (
          <AutoBreadcrumbs pageTitle={pageTitle} />
        ) : (
          <AutoBreadcrumbs />
        )}
      </div>
    </div>
  </div>
)}

<style>
  .breadcrumb-section {
    background: white;
    border-bottom: 1px solid #f1f5f9;
    padding: 0.75rem 0;
  }

  .breadcrumb-container {
    margin: 0;
    padding: 0;
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .breadcrumb-section {
      padding: 0.5rem 0;
    }
  }
</style>
