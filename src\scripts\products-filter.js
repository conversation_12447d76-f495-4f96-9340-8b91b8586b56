/**
 * Products Filter and Sort System
 * Handles client-side filtering and sorting of products for better performance
 */

window.ProductsFilter = {
  // Initialize the filter system
  init() {
    const categoryFilter = document.getElementById('category-filter');
    const searchInput = document.getElementById('product-search');
    const sortBy = document.getElementById('sort-by');
    const noResults = document.getElementById('no-results');
    const resultsCount = document.getElementById('results-count');
    
    // Store references for performance
    this.categoryFilter = categoryFilter;
    this.searchInput = searchInput;
    this.sortBy = sortBy;
    this.noResults = noResults;
    this.resultsCount = resultsCount;
    
    // Bind events with performance optimization
    this.bindEvents();
    
    // Apply URL parameters on initial load
    this.applyUrlParameters();
  },

  // Debounce function for performance
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  // Main filter and sort function
  filterAndSortProducts() {
    const category = this.categoryFilter?.value || '';
    const search = this.searchInput?.value.toLowerCase() || '';
    const sortValue = this.sortBy?.value || 'newest';
    
    const productCards = Array.from(document.querySelectorAll('.product-card'));
    let visibleCards = [];
    
    // Filter products
    productCards.forEach(card => {
      const cardCategory = card.dataset.category || '';
      const cardName = card.dataset.name.toLowerCase();
      const cardDesc = card.dataset.description.toLowerCase();
      
      const categoryMatch = !category || cardCategory === category;
      const searchMatch = !search || cardName.includes(search) || cardDesc.includes(search);
      
      if (categoryMatch && searchMatch) {
        visibleCards.push(card);
        card.style.display = '';
      } else {
        card.style.display = 'none';
      }
    });
    
    // Sort visible products
    visibleCards.sort((a, b) => {
      switch (sortValue) {
        case 'price-low':
          return parseFloat(a.dataset.price) - parseFloat(b.dataset.price);
        case 'price-high':
          return parseFloat(b.dataset.price) - parseFloat(a.dataset.price);
        case 'newest':
        default:
          return parseInt(b.dataset.index || '0') - parseInt(a.dataset.index || '0');
        case 'name':
          return a.dataset.name.localeCompare(b.dataset.name);
      }
    });
    
    // Reorder DOM elements efficiently
    const container = document.getElementById('products-list');
    if (container) {
      const fragment = document.createDocumentFragment();
      visibleCards.forEach(card => fragment.appendChild(card));
      container.appendChild(fragment);
    }

    // Notify pagination system about filtered products
    if (window.productsPagination) {
      window.productsPagination.updateFilteredProducts(visibleCards);
    } else {
      // Dispatch event for pagination system
      document.dispatchEvent(new CustomEvent('productsFiltered', {
        detail: { visibleProducts: visibleCards }
      }));
    }

    // Update results count
    if (this.resultsCount) {
      this.resultsCount.textContent = `${visibleCards.length} product${visibleCards.length !== 1 ? 's' : ''}`;
    }

    // Show/hide no results message
    if (this.noResults) {
      this.noResults.style.display = visibleCards.length === 0 ? 'block' : 'none';
    }

    // Update URL parameters for better UX
    this.updateUrlParameters(category, search, sortValue);
  },

  // Bind event listeners
  bindEvents() {
    if (this.categoryFilter) {
      this.categoryFilter.addEventListener('change', () => {
        this.filterAndSortProducts();
      }, { passive: true });
    }

    if (this.searchInput) {
      this.searchInput.addEventListener('input', this.debounce(() => {
        this.filterAndSortProducts();
      }, 300), { passive: true });
    }

    if (this.sortBy) {
      this.sortBy.addEventListener('change', () => {
        this.filterAndSortProducts();
      }, { passive: true });
    }
  },

  // Apply URL parameters on initial load
  applyUrlParameters() {
    const url = new URL(window.location.href);
    const categoryParam = url.searchParams.get('category');
    const searchParam = url.searchParams.get('search');
    const sortParam = url.searchParams.get('sort');
    
    if (categoryParam && this.categoryFilter) {
      this.categoryFilter.value = categoryParam;
    }
    
    if (searchParam && this.searchInput) {
      this.searchInput.value = searchParam;
    }
    
    if (sortParam && this.sortBy) {
      this.sortBy.value = sortParam;
    }
    
    if (categoryParam || searchParam || sortParam) {
      this.filterAndSortProducts();
    }
  },

  // Update URL parameters without page reload
  updateUrlParameters(category, search, sort) {
    const url = new URL(window.location.href);
    
    // Update or remove parameters
    if (category) {
      url.searchParams.set('category', category);
    } else {
      url.searchParams.delete('category');
    }
    
    if (search) {
      url.searchParams.set('search', search);
    } else {
      url.searchParams.delete('search');
    }
    
    if (sort && sort !== 'name') {
      url.searchParams.set('sort', sort);
    } else {
      url.searchParams.delete('sort');
    }
    
    // Update URL without page reload
    window.history.replaceState({}, '', url.toString());
  },

  // Reset all filters
  resetFilters() {
    if (this.categoryFilter) this.categoryFilter.value = '';
    if (this.searchInput) this.searchInput.value = '';
    if (this.sortBy) this.sortBy.value = 'name';
    
    this.filterAndSortProducts();
  },

  // Get current filter state
  getCurrentFilters() {
    return {
      category: this.categoryFilter?.value || '',
      search: this.searchInput?.value || '',
      sort: this.sortBy?.value || 'name'
    };
  }
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.ProductsFilter.init();
  });
} else {
  window.ProductsFilter.init();
}
