/**
 * Service Worker Registration and Management
 * Handles registration, updates, and cache management
 */

export interface ServiceWorkerConfig {
  enabled: boolean;
  swPath: string;
  scope: string;
  updateCheckInterval: number;
}

export const DEFAULT_SW_CONFIG: ServiceWorkerConfig = {
  enabled: true,
  swPath: '/sw.js',
  scope: '/',
  updateCheckInterval: 60000 // Check for updates every minute
};

/**
 * Register service worker
 */
export async function registerServiceWorker(config: Partial<ServiceWorkerConfig> = {}): Promise<ServiceWorkerRegistration | null> {
  const finalConfig = { ...DEFAULT_SW_CONFIG, ...config };
  
  // Check if service workers are supported
  if (!('serviceWorker' in navigator)) {
    console.warn('Service Workers not supported');
    return null;
  }
  
  // Skip registration if disabled
  if (!finalConfig.enabled) {
    console.log('Service Worker registration disabled');
    return null;
  }
  
  try {
    console.log('Registering Service Worker...');
    
    const registration = await navigator.serviceWorker.register(finalConfig.swPath, {
      scope: finalConfig.scope
    });
    
    console.log('Service Worker registered successfully:', registration);
    
    // Handle updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      if (newWorker) {
        console.log('New Service Worker found, installing...');
        
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            console.log('New Service Worker installed, ready to activate');
            
            // Optionally show update notification to user
            showUpdateNotification(registration);
          }
        });
      }
    });
    
    // Check for updates periodically
    setInterval(() => {
      registration.update();
    }, finalConfig.updateCheckInterval);
    
    return registration;
  } catch (error) {
    console.error('Service Worker registration failed:', error);
    return null;
  }
}

/**
 * Show update notification to user
 */
function showUpdateNotification(registration: ServiceWorkerRegistration) {
  // Create a simple notification
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #2563eb;
    color: white;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 10000;
    font-family: system-ui, -apple-system, sans-serif;
    font-size: 14px;
    max-width: 300px;
    display: flex;
    align-items: center;
    gap: 12px;
  `;
  
  notification.innerHTML = `
    <div>
      <div style="font-weight: 600; margin-bottom: 4px;">Update Available</div>
      <div style="opacity: 0.9;">A new version is ready. Refresh to update.</div>
    </div>
    <button style="
      background: rgba(255,255,255,0.2);
      border: none;
      color: white;
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
    ">Refresh</button>
  `;
  
  const refreshButton = notification.querySelector('button');
  if (refreshButton) {
    refreshButton.addEventListener('click', () => {
      // Tell the new service worker to skip waiting
      if (registration.waiting) {
        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      }
      window.location.reload();
    });
  }
  
  document.body.appendChild(notification);
  
  // Auto-remove after 10 seconds
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 10000);
}

/**
 * Unregister service worker
 */
export async function unregisterServiceWorker(): Promise<boolean> {
  if (!('serviceWorker' in navigator)) {
    return false;
  }
  
  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      const result = await registration.unregister();
      console.log('Service Worker unregistered:', result);
      return result;
    }
    return false;
  } catch (error) {
    console.error('Service Worker unregistration failed:', error);
    return false;
  }
}

/**
 * Clear all caches
 */
export async function clearAllCaches(): Promise<void> {
  if (!('caches' in window)) {
    console.warn('Cache API not supported');
    return;
  }
  
  try {
    const cacheNames = await caches.keys();
    await Promise.all(
      cacheNames.map(cacheName => {
        console.log('Deleting cache:', cacheName);
        return caches.delete(cacheName);
      })
    );
    console.log('All caches cleared');
  } catch (error) {
    console.error('Failed to clear caches:', error);
  }
}

/**
 * Get cache storage usage
 */
export async function getCacheStorageUsage(): Promise<{ used: number; quota: number } | null> {
  if (!('storage' in navigator) || !('estimate' in navigator.storage)) {
    console.warn('Storage API not supported');
    return null;
  }
  
  try {
    const estimate = await navigator.storage.estimate();
    return {
      used: estimate.usage || 0,
      quota: estimate.quota || 0
    };
  } catch (error) {
    console.error('Failed to get storage estimate:', error);
    return null;
  }
}

/**
 * Preload critical resources
 */
export function preloadCriticalResources(urls: string[]): void {
  urls.forEach(url => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = url;
    
    // Determine resource type
    if (url.includes('.css')) {
      link.as = 'style';
    } else if (url.includes('.js')) {
      link.as = 'script';
    } else if (url.match(/\.(jpg|jpeg|png|webp|avif|svg)$/i)) {
      link.as = 'image';
    } else if (url.match(/\.(woff|woff2)$/i)) {
      link.as = 'font';
      link.crossOrigin = 'anonymous';
    }
    
    document.head.appendChild(link);
  });
}

/**
 * Initialize service worker and cache optimization
 */
export async function initializeCacheOptimization(config: Partial<ServiceWorkerConfig> = {}): Promise<void> {
  // Register service worker
  const registration = await registerServiceWorker(config);
  
  if (registration) {
    console.log('Cache optimization initialized successfully');
    
    // Preload critical resources
    const criticalResources = [
      '/favicon.svg',
      '/images/logo.svg',
      '/_astro/global.css' // Adjust based on your actual CSS file
    ];
    
    preloadCriticalResources(criticalResources);
    
    // Log cache usage
    const usage = await getCacheStorageUsage();
    if (usage) {
      console.log(`Cache usage: ${(usage.used / 1024 / 1024).toFixed(2)} MB / ${(usage.quota / 1024 / 1024).toFixed(2)} MB`);
    }
  }
}

// Auto-initialize on load (can be disabled by setting window.disableAutoSW = true)
if (typeof window !== 'undefined' && !(window as any).disableAutoSW) {
  document.addEventListener('DOMContentLoaded', () => {
    initializeCacheOptimization();
  });
}
