import { defineConfig } from 'astro/config';

export default defineConfig({
  output: 'static',
  site: 'https://www.cheersmarketplace.com',
  base: '/',
  trailingSlash: 'always',

  // Optimized build configuration for performance
  build: {
    target: 'es2022',
    inlineStylesheets: 'auto'
  },

  // Ensure public files are copied
  publicDir: './public',

  // Simplified Vite configuration for Cloudflare compatibility
  vite: {
    build: {
      target: 'es2022',
      minify: 'esbuild',
      sourcemap: false
    },
    optimizeDeps: {
      exclude: ['sharp']
    }
  },

  // Simplified image configuration
  image: {
    service: {
      entrypoint: 'astro/assets/services/sharp'
    }
  },


});
