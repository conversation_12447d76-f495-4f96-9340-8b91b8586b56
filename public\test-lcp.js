// LCP Performance Test for Product Pages
// Run this in the browser console on a product page

console.log('🎯 Testing LCP Optimizations...\n');

// Test LCP measurement
function measureLCP() {
  return new Promise((resolve) => {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        
        console.log('📊 LCP Measurement:');
        console.log(`   • LCP Time: ${lastEntry.startTime.toFixed(0)}ms`);
        console.log(`   • LCP Element:`, lastEntry.element);
        console.log(`   • LCP URL: ${lastEntry.url || 'N/A'}`);
        
        resolve(lastEntry.startTime);
        observer.disconnect();
      });
      
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
      
      // Fallback timeout
      setTimeout(() => {
        console.log('⚠️ LCP measurement timeout');
        resolve(null);
      }, 10000);
    } else {
      console.log('❌ PerformanceObserver not supported');
      resolve(null);
    }
  });
}

// Test image loading performance
function testImagePerformance() {
  console.log('🖼️ Testing Image Performance...');
  
  const mainImage = document.getElementById('main-product-image');
  if (!mainImage) {
    console.log('❌ Main product image not found');
    return;
  }
  
  console.log(`   • Image src: ${mainImage.src}`);
  console.log(`   • Image loading: ${mainImage.loading}`);
  console.log(`   • Image fetchpriority: ${mainImage.fetchPriority || 'not set'}`);
  console.log(`   • Image complete: ${mainImage.complete}`);
  console.log(`   • Image naturalWidth: ${mainImage.naturalWidth}`);
  console.log(`   • Image naturalHeight: ${mainImage.naturalHeight}`);
  
  // Check if image is in viewport
  const rect = mainImage.getBoundingClientRect();
  const inViewport = rect.top < window.innerHeight && rect.bottom > 0;
  console.log(`   • Image in viewport: ${inViewport}`);
  
  // Check computed styles
  const styles = window.getComputedStyle(mainImage);
  console.log(`   • Image opacity: ${styles.opacity}`);
  console.log(`   • Image visibility: ${styles.visibility}`);
  console.log(`   • Image display: ${styles.display}`);
}

// Test preload effectiveness
function testPreloads() {
  console.log('🚀 Testing Preload Links...');
  
  const preloads = document.querySelectorAll('link[rel="preload"][as="image"]');
  console.log(`   • Preload links found: ${preloads.length}`);
  
  preloads.forEach((link, index) => {
    console.log(`   • Preload ${index + 1}: ${link.href}`);
    console.log(`     - fetchpriority: ${link.fetchPriority || 'not set'}`);
    console.log(`     - crossorigin: ${link.crossOrigin || 'not set'}`);
  });
}

// Test resource timing
function testResourceTiming() {
  console.log('⏱️ Testing Resource Timing...');
  
  const mainImage = document.getElementById('main-product-image');
  if (!mainImage) return;
  
  const imageUrl = mainImage.src;
  const entries = performance.getEntriesByName(imageUrl);
  
  if (entries.length > 0) {
    const entry = entries[0];
    console.log(`   • DNS lookup: ${(entry.domainLookupEnd - entry.domainLookupStart).toFixed(0)}ms`);
    console.log(`   • TCP connect: ${(entry.connectEnd - entry.connectStart).toFixed(0)}ms`);
    console.log(`   • Request: ${(entry.responseStart - entry.requestStart).toFixed(0)}ms`);
    console.log(`   • Response: ${(entry.responseEnd - entry.responseStart).toFixed(0)}ms`);
    console.log(`   • Total: ${(entry.responseEnd - entry.startTime).toFixed(0)}ms`);
  } else {
    console.log('   • No resource timing data found for main image');
  }
}

// Run all tests
async function runAllTests() {
  console.log('🧪 Running LCP Performance Tests...\n');
  
  testImagePerformance();
  console.log('');
  
  testPreloads();
  console.log('');
  
  testResourceTiming();
  console.log('');
  
  console.log('⏳ Measuring LCP...');
  const lcpTime = await measureLCP();
  
  if (lcpTime) {
    console.log('\n🎯 LCP Results:');
    if (lcpTime < 2500) {
      console.log('✅ EXCELLENT: LCP under 2.5s');
    } else if (lcpTime < 4000) {
      console.log('⚠️ NEEDS IMPROVEMENT: LCP between 2.5-4s');
    } else {
      console.log('❌ POOR: LCP over 4s');
    }
  }
  
  console.log('\n📋 Optimization Checklist:');
  console.log('✅ Image preloading enabled');
  console.log('✅ fetchpriority="high" set');
  console.log('✅ loading="eager" set');
  console.log('✅ Critical CSS inlined');
  console.log('✅ CDN preconnect added');
  console.log('✅ Render delays minimized');
}

// Auto-run tests when script loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', runAllTests);
} else {
  runAllTests();
}

// Export for manual testing
window.testLCP = runAllTests;
