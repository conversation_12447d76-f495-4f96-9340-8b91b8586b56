---
/**
 * Optimized Snipcart Loader Component
 * Reduces unused JavaScript by implementing advanced lazy loading
 * and only loading Snipcart when actually needed
 */

export interface Props {
  publicApiKey?: string;
  currency?: string;
  modalStyle?: 'side' | 'standard';
  addProductBehavior?: 'none' | 'default';
}

const {
  publicApiKey = (import.meta as any).env?.PUBLIC_SNIPCART_API_KEY || 'YTY0ZTIxNjYtNDVlMC00NmUyLWFkNjItYTg3ZmNhMTc4MDQyNjM4ODczNzI4NTM0MTY0NDA4',
  currency = 'usd',
  modalStyle = 'side',
  addProductBehavior = 'none'
} = Astro.props;
---

<!-- Optimized Snipcart v3 Integration with Advanced Lazy Loading -->
<script define:vars={{
  snipcartPublicKey: publicApiKey,
  snipcartCurrency: currency,
  snipcartModalStyle: modalStyle,
  snipcartAddProductBehavior: addProductBehavior
}} is:inline defer>
  // Advanced Snipcart loader with performance optimizations
  window.SnipcartSettings = {
    publicApiKey: snipcartPublicKey,
    loadStrategy: "manual", // We'll handle loading manually for better control
    currency: snipcartCurrency,
    addProductBehavior: snipcartAddProductBehavior,
    modalStyle: snipcartModalStyle,
    version: "3.7.1", // Use latest stable version for better performance
    timeoutDuration: 5000, // Increased timeout for better reliability
    domain: "cdn.snipcart.com",
    protocol: "https",
    loadCSS: true
  };

  // Performance-optimized Snipcart loader
  (function() {
    let snipcartLoaded = false;
    let snipcartLoading = false;
    let loadAttempts = 0;
    const maxLoadAttempts = 3;
    
    // Optimized event listeners - only essential ones for cart interaction
    const triggerEvents = ['click', 'touchstart'];
    const snipcartSelectors = [
      '.snipcart-add-item',
      '.snipcart-checkout',
      '.snipcart-customer-signin',
      '.snipcart-items-count',
      '[data-item-id]',
      '[class*="snipcart"]'
    ];
    
    function loadSnipcart() {
      if (snipcartLoaded || snipcartLoading) return Promise.resolve();
      if (loadAttempts >= maxLoadAttempts) {
        console.warn('Max Snipcart load attempts reached');
        return Promise.reject(new Error('Max load attempts reached'));
      }
      
      snipcartLoading = true;
      loadAttempts++;
      
      return new Promise((resolve, reject) => {
        // Remove event listeners to prevent multiple loads
        triggerEvents.forEach(event => {
          document.removeEventListener(event, handleSnipcartTrigger, true);
        });
        
        // Create script element with optimizations
        const script = document.createElement('script');
        script.src = `${window.SnipcartSettings.protocol}://${window.SnipcartSettings.domain}/themes/v${window.SnipcartSettings.version}/default/snipcart.js`;
        script.async = true;
        script.defer = true;
        
        // Add loading handlers
        script.onload = function() {
          snipcartLoaded = true;
          snipcartLoading = false;
          console.log('Snipcart loaded successfully');
          
          // Initialize Snipcart container if not exists
          initializeSnipcartContainer();
          resolve();
        };
        
        script.onerror = function() {
          console.error('Failed to load Snipcart, attempt:', loadAttempts);
          snipcartLoading = false;
          
          // Re-add event listeners for retry
          if (loadAttempts < maxLoadAttempts) {
            setTimeout(() => {
              triggerEvents.forEach(event => {
                document.addEventListener(event, handleSnipcartTrigger, true);
              });
            }, 1000);
          }
          reject(new Error('Failed to load Snipcart'));
        };
        
        document.head.appendChild(script);
        
        // Load CSS separately for better performance
        loadSnipcartCSS();
      });
    }
    
    function loadSnipcartCSS() {
      if (document.querySelector('link[href*="snipcart.css"]')) return;
      
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.type = 'text/css';
      link.href = `${window.SnipcartSettings.protocol}://${window.SnipcartSettings.domain}/themes/v${window.SnipcartSettings.version}/default/snipcart.css`;
      link.media = 'print';
      link.onload = function() {
        this.media = 'all';
      };
      document.head.appendChild(link);
    }
    
    function initializeSnipcartContainer() {
      if (document.getElementById('snipcart')) return;
      
      const container = document.createElement('div');
      container.id = 'snipcart';
      container.setAttribute('data-api-key', window.SnipcartSettings.publicApiKey);
      container.setAttribute('data-config-modal-style', window.SnipcartSettings.modalStyle);
      container.setAttribute('data-config-add-product-behavior', window.SnipcartSettings.addProductBehavior);
      container.setAttribute('data-currency', window.SnipcartSettings.currency);
      container.setAttribute('hidden', 'true');
      document.body.appendChild(container);
    }
    
    function handleSnipcartTrigger(event) {
      // Check if the clicked element or its parent is a Snipcart trigger
      const target = event.target.closest(snipcartSelectors.join(', '));
      if (target) {
        event.preventDefault();
        event.stopPropagation();
        
        // Show loading state
        const originalText = target.textContent;
        if (target.classList.contains('snipcart-add-item')) {
          target.textContent = 'Loading...';
          target.disabled = true;
        }
        
        loadSnipcart().then(() => {
          // Restore original state
          if (target.classList.contains('snipcart-add-item')) {
            target.textContent = originalText;
            target.disabled = false;
          }
          
          // Re-trigger the event after Snipcart loads
          setTimeout(() => {
            if (target.click && typeof target.click === 'function') {
              target.click();
            }
          }, 100);
        }).catch(() => {
          // Restore original state on error
          if (target.classList.contains('snipcart-add-item')) {
            target.textContent = originalText;
            target.disabled = false;
          }
          console.error('Failed to load Snipcart for interaction');
        });
      }
    }
    
    // Add optimized event listeners
    triggerEvents.forEach(event => {
      document.addEventListener(event, handleSnipcartTrigger, true);
    });
    
    // Preload Snipcart resources on idle for better UX
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        // Preload DNS and establish connection
        const preconnectLink = document.createElement('link');
        preconnectLink.rel = 'preconnect';
        preconnectLink.href = `${window.SnipcartSettings.protocol}://${window.SnipcartSettings.domain}`;
        preconnectLink.crossOrigin = 'anonymous';
        document.head.appendChild(preconnectLink);
        
        // Prefetch the script for faster loading when needed
        const prefetchLink = document.createElement('link');
        prefetchLink.rel = 'prefetch';
        prefetchLink.href = `${window.SnipcartSettings.protocol}://${window.SnipcartSettings.domain}/themes/v${window.SnipcartSettings.version}/default/snipcart.js`;
        document.head.appendChild(prefetchLink);
      }, { timeout: 2000 });
    }
    
    // Expose load function globally for manual triggering
    window.loadSnipcart = loadSnipcart;
  })();
</script>

<!-- Optimized Snipcart Custom Styling -->
<style is:global>
  /* Ensure Snipcart modal appears above everything including toasts */
  .snipcart-modal__container {
    z-index: 10001 !important;
  }

  .snipcart__modal {
    z-index: 10001 !important;
  }

  /* Match Cheers Marketplace branding */
  .snipcart-layout {
    font-family: system-ui, -apple-system, sans-serif !important;
  }

  .snipcart__box--badge-highlight {
    background-color: #92400e !important;
  }

  .snipcart__button--primary {
    background-color: #92400e !important;
    border-color: #92400e !important;
  }

  .snipcart__button--primary:hover {
    background-color: #78350f !important;
    border-color: #78350f !important;
  }

  /* Side modal specific styling */
  .snipcart__modal--side {
    font-family: system-ui, -apple-system, sans-serif !important;
  }

  /* Loading state for add to cart buttons */
  .snipcart-add-item:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  /* Optimize cart count display */
  .snipcart-items-count {
    display: inline-block;
    min-width: 1.2em;
    text-align: center;
  }

  /* Hide Snipcart container until needed */
  #snipcart[hidden] {
    display: none !important;
  }
</style>
