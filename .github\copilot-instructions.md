<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

This project is a highly optimized Astro storefront for Cheers Marketplace, designed for Cloudflare Pages. Prioritize edge performance, minimal JavaScript, and clean, maintainable code. Use Astro best practices for routing, data, and deployment.

Use the #sequentialthinking MCP server and #context7 MCP server throughout development.