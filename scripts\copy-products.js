#!/usr/bin/env node

/**
 * Copy products.json from src/data to public/data for runtime access
 * This ensures the admin panel and debug tools can access the products data
 */

import { readFileSync, writeFileSync, mkdirSync } from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

try {
  console.log('📦 Copying products data to public folder...');
  
  // Read source file
  const srcPath = join(projectRoot, 'src/data/products.json');
  const destPath = join(projectRoot, 'public/data/products.json');
  
  // Ensure destination directory exists
  mkdirSync(dirname(destPath), { recursive: true });
  
  // Read and parse products data
  const productsData = readFileSync(srcPath, 'utf8');
  const products = JSON.parse(productsData);
  
  console.log(`📊 Found ${products.length} products to copy`);
  
  // Write to public folder
  writeFileSync(destPath, productsData);
  
  console.log('✅ Products data copied successfully');
  console.log(`   Source: ${srcPath}`);
  console.log(`   Destination: ${destPath}`);
  
} catch (error) {
  console.error('❌ Error copying products data:', error.message);
  process.exit(1);
}
