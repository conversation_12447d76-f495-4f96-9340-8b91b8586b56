/**
 * Cache management utilities for Cloudflare integration
 */

export interface CachePurgeOptions {
  zoneId: string;
  apiToken: string;
  urls?: string[];
  tags?: string[];
  purgeEverything?: boolean;
}

export interface CachePurgeResult {
  success: boolean;
  purgeId?: string;
  error?: string;
  urls?: string[];
  timestamp: string;
}

/**
 * Purge Cloudflare cache using the Cloudflare API
 */
export async function purgeCloudflareCache(options: CachePurgeOptions): Promise<CachePurgeResult> {
  const { zoneId, apiToken, urls, tags, purgeEverything } = options;
  
  try {
    const purgeEndpoint = `https://api.cloudflare.com/client/v4/zones/${zoneId}/purge_cache`;
    
    // Determine what to purge
    let purgeData: any = {};
    
    if (purgeEverything) {
      purgeData.purge_everything = true;
    } else if (urls && urls.length > 0) {
      purgeData.files = urls;
    } else if (tags && tags.length > 0) {
      purgeData.tags = tags;
    } else {
      // Default to purging everything if no specific targets
      purgeData.purge_everything = true;
    }
    
    console.log('Cloudflare cache purge request:', purgeData);
    
    const response = await fetch(purgeEndpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(purgeData)
    });

    const result = await response.json();
    
    if (response.ok && result.success) {
      console.log('✅ Cloudflare cache purged successfully');
      return {
        success: true,
        purgeId: result.result?.id,
        urls: urls,
        timestamp: new Date().toISOString()
      };
    } else {
      console.error('❌ Cloudflare cache purge failed:', result);
      return {
        success: false,
        error: result.errors?.[0]?.message || 'Unknown error',
        timestamp: new Date().toISOString()
      };
    }
  } catch (error) {
    console.error('❌ Cloudflare cache purge error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Get critical URLs that should be purged when products change
 */
export function getCriticalUrls(baseUrl: string): string[] {
  return [
    `${baseUrl}/`,
    `${baseUrl}/products/`,
    `${baseUrl}/sitemap.xml`,
    // Add product category pages if you have them
    `${baseUrl}/products/electronics/`,
    `${baseUrl}/products/clothing/`,
    `${baseUrl}/products/books/`,
    `${baseUrl}/products/home-decor/`,
    `${baseUrl}/products/arts-crafts/`
  ];
}

/**
 * Get product-specific URLs that should be purged
 */
export function getProductUrls(baseUrl: string, productSlugs: string[]): string[] {
  return productSlugs.map(slug => `${baseUrl}/products/${slug}/`);
}

/**
 * Purge cache for product-related changes
 */
export async function purgeProductCache(
  zoneId: string, 
  apiToken: string, 
  baseUrl: string, 
  changedProductSlugs?: string[]
): Promise<CachePurgeResult> {
  const criticalUrls = getCriticalUrls(baseUrl);
  
  // Add specific product URLs if provided
  const productUrls = changedProductSlugs ? getProductUrls(baseUrl, changedProductSlugs) : [];
  
  const allUrls = [...criticalUrls, ...productUrls];
  
  return purgeCloudflareCache({
    zoneId,
    apiToken,
    urls: allUrls
  });
}

/**
 * Purge cache by tags
 */
export async function purgeCacheByTags(
  zoneId: string,
  apiToken: string,
  tags: string[]
): Promise<CachePurgeResult> {
  if (!tags || tags.length === 0) {
    console.warn('purgeCacheByTags called with no tags. Skipping.');
    return {
      success: false,
      error: 'No tags provided for purging.',
      timestamp: new Date().toISOString()
    };
  }

  return purgeCloudflareCache({
    zoneId,
    apiToken,
    tags
  });
}

/**
 * Check if cache purging is properly configured
 */
export function isCachePurgingConfigured(env: any): boolean {
  return !!(env?.CLOUDFLARE_ZONE_ID && env?.CLOUDFLARE_API_TOKEN);
}

/**
 * Get cache configuration status
 */
export function getCacheConfig(env: any) {
  return {
    configured: isCachePurgingConfigured(env),
    zoneId: !!env?.CLOUDFLARE_ZONE_ID,
    apiToken: !!env?.CLOUDFLARE_API_TOKEN,
    missing: [
      ...(!env?.CLOUDFLARE_ZONE_ID ? ['CLOUDFLARE_ZONE_ID'] : []),
      ...(!env?.CLOUDFLARE_API_TOKEN ? ['CLOUDFLARE_API_TOKEN'] : [])
    ]
  };
}

/**
 * Cache tags for different content types
 */
export const CACHE_TAGS = {
  PRODUCTS: 'products',
  PRODUCT_LIST: 'product-list',
  PRODUCT_DETAIL: 'product-detail',
  CATEGORIES: 'categories',
  SITEMAP: 'sitemap',
  API: 'api'
} as const;

/**
 * Generate cache tags for a product
 */
export function getProductCacheTags(productId: string, category: string): string[] {
  return [
    CACHE_TAGS.PRODUCTS,
    CACHE_TAGS.PRODUCT_DETAIL,
    `product-${productId}`,
    `category-${category.toLowerCase().replace(/\s+/g, '-')}`
  ];
}

/**
 * Generate cache tags for product listings
 */
export function getProductListCacheTags(): string[] {
  return [
    CACHE_TAGS.PRODUCTS,
    CACHE_TAGS.PRODUCT_LIST,
    CACHE_TAGS.CATEGORIES
  ];
}
