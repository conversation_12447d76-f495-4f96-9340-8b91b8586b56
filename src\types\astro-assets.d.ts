// Type declarations for Astro assets module
// This provides TypeScript support for astro:assets imports

declare module 'astro:assets' {
  export interface ImageMetadata {
    src: string;
    width: number;
    height: number;
    format: string;
  }

  export interface GetImageOptions {
    src: string | ImageMetadata;
    alt?: string;
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'avif' | 'png' | 'jpg' | 'jpeg' | 'svg';
    loading?: 'lazy' | 'eager';
    decoding?: 'async' | 'sync' | 'auto';
    fetchpriority?: 'high' | 'low' | 'auto';
    sizes?: string;
    class?: string;
    style?: string;
  }

  export interface ImageProps extends GetImageOptions {
    alt: string;
  }

  export const Image: (props: ImageProps) => any;
  export const Picture: (props: ImageProps) => any;
  export function getImage(options: GetImageOptions): Promise<{
    src: string;
    width: number;
    height: number;
    format: string;
  }>;
}
