---
export const prerender = true;

import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import { generateHomepageDescription } from '../utils/metaDescriptions.ts';

// Generate SEO-optimized meta description
const metaDescription = generateHomepageDescription();
const pageTitle = "Cheers Marketplace - Quality Secondhand Goods in Chico, CA";
---

<Layout
  title={pageTitle}
  description={metaDescription}
>
  <Header />
  <main>
    <div class="container">
      <!-- Hero Section -->
      <section class="hero-section">
        <div class="hero-content">
          <h1>Welcome to Cheers Marketplace</h1>
          <p class="hero-subtitle">A Family-Run Business in Chico, California</p>
          <p class="hero-description">Your trusted destination for quality used goods at unbeatable prices. Every item is personally inspected by our husband-and-wife team to ensure exceptional value.</p>
        </div>
      </section>

    <!-- About Us Preview -->
    <section class="about-preview">
      <div class="about-content">
        <div class="about-text">
          <h2>Our Family Business</h2>
          <p>We're a dedicated husband-and-wife team based in Chico, CA, passionate about providing quality secondhand goods at affordable prices. Our personal touch means every item receives careful inspection before reaching you.</p>
        </div>
        <div class="about-visual">
          <div class="family-badge">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
              <circle cx="9" cy="7" r="4"/>
              <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
            </svg>
            <span>Family Owned</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Why Choose Us -->
    <section class="features-section">
      <h2>Why Choose Cheers Marketplace?</h2>
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"/>
              <path d="M21 21l-4.35-4.35"/>
            </svg>
          </div>
          <h3>Personal Inspection</h3>
          <p>Every item is carefully examined by us for quality, cleanliness, and condition before listing.</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
            </svg>
          </div>
          <h3>Personal Service</h3>
          <p>Direct communication with the owners who care about your satisfaction and shopping experience.</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
            </svg>
          </div>
          <h3>Quality Assurance</h3>
          <p>We stand behind every item with honest descriptions and transparent condition reporting.</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="12" y1="1" x2="12" y2="23"/>
              <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
            </svg>
          </div>
          <h3>Affordable Prices</h3>
          <p>Budget-friendly options without compromising on quality - exceptional value for your money.</p>
        </div>
      </div>
    </section>

    <!-- Important Disclaimers -->
    <section class="disclaimers-section">
      <h2>Important Information</h2>
      <div class="disclaimers-grid">
        <div class="disclaimer-card color-disclaimer">
          <div class="disclaimer-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
              <circle cx="12" cy="12" r="3"/>
            </svg>
          </div>
          <h3>Color Accuracy Notice</h3>
          <p>Colors may appear different due to lighting conditions and display settings. We strive for accuracy but recommend considering potential variations.</p>
        </div>

        <div class="disclaimer-card allergen-disclaimer">
          <div class="disclaimer-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </div>
          <h3>Pet-Friendly Household</h3>
          <p>Please note that clothing items may contain cat allergens as we share our home with beloved feline family members.</p>
        </div>
      </div>
    </section>

    <!-- Call to Action -->
    <section class="cta-section">
      <div class="cta-content">
        <h2>Ready to Find Your Next Treasure?</h2>
        <p>Browse our carefully curated collection of quality used goods, all personally inspected and ready for their next home.</p>
        <a href="/products" class="cta-btn primary">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h7"/>
          </svg>
          Start Shopping
        </a>
        <a href="/about" class="cta-btn secondary">Learn More About Us</a>
      </div>
    </section>
    </div>
  </main>
  <Footer />
</Layout>

<style>
  /* Hero Section */
  .hero-section {
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border);
    padding: 2.5rem 2rem;
    margin: 1.5rem auto 1rem auto;
    max-width: 800px;
    width: 100%;
    text-align: center;
  }

  .hero-content h1 {
    font-family: Georgia, 'Times New Roman', Times, serif;
    color: var(--text);
    font-size: 2.5rem;
    font-weight: 600;
    margin: 0 0 0.75rem 0;
    letter-spacing: -0.025em;
    line-height: 1.1;
  }

  .hero-subtitle {
    font-size: 1.125rem;
    color: var(--primary);
    font-weight: 600;
    margin: 0 0 1rem 0;
    letter-spacing: -0.01em;
  }

  .hero-description {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
  }

  /* About Preview Section */
  .about-preview {
    background: var(--border-light);
    border-radius: var(--radius-xl);
    padding: 2rem;
    margin: 1rem auto;
    max-width: 800px;
    width: 100%;
  }

  .about-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1.5rem;
    align-items: center;
  }

  .about-text h2 {
    font-family: Georgia, 'Times New Roman', Times, serif;
    color: var(--text);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 0.75rem 0;
    letter-spacing: -0.025em;
  }

  .about-text p {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
  }

  .family-badge {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    background: var(--primary);
    color: white;
    padding: 1rem;
    border-radius: var(--radius-lg);
    text-align: center;
    min-width: 100px;
  }

  .family-badge span {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  /* Features Section */
  .features-section {
    padding: 2rem 0;
    max-width: 1000px;
    width: 100%;
    margin: 0 auto;
  }

  .features-section h2 {
    font-family: Georgia, 'Times New Roman', Times, serif;
    color: var(--text);
    font-size: 1.75rem;
    font-weight: 600;
    text-align: center;
    margin: 0 0 2rem 0;
    letter-spacing: -0.025em;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.25rem;
  }

  .feature-card {
    background: var(--card-bg);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    text-align: center;
    border: 1px solid var(--border);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
  }

  .feature-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary);
  }

  .feature-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: var(--primary);
    color: white;
    border-radius: 50%;
    margin: 0 auto 1rem auto;
  }

  .feature-card h3 {
    font-family: Georgia, 'Times New Roman', Times, serif;
    color: var(--text);
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 0.75rem 0;
    letter-spacing: -0.01em;
  }

  .feature-card p {
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
    font-size: 0.875rem;
  }

  /* Disclaimers Section */
  .disclaimers-section {
    padding: 1.5rem 0;
    max-width: 800px;
    width: 100%;
    margin: 0 auto;
  }

  .disclaimers-section h2 {
    font-family: Georgia, 'Times New Roman', Times, serif;
    color: var(--text);
    font-size: 1.5rem;
    font-weight: 600;
    text-align: center;
    margin: 0 0 1.25rem 0;
    letter-spacing: -0.025em;
  }

  .disclaimers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
  }

  .disclaimer-card {
    background: #fef3c7;
    border: 1px solid #fde68a;
    border-radius: var(--radius-lg);
    padding: 1.25rem;
    display: flex;
    gap: 0.75rem;
    align-items: flex-start;
  }

  .disclaimer-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: #f59e0b;
    color: white;
    border-radius: 50%;
    flex-shrink: 0;
  }

  .disclaimer-card h3 {
    color: #92400e;
    font-size: 0.95rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    letter-spacing: -0.01em;
  }

  .disclaimer-card p {
    color: #92400e;
    font-size: 0.8rem;
    line-height: 1.4;
    margin: 0;
  }

  /* CTA Section */
  .cta-section {
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border);
    padding: 2rem;
    margin: 1rem auto;
    max-width: 700px;
    width: 100%;
    text-align: center;
  }

  .cta-content h2 {
    font-family: Georgia, 'Times New Roman', Times, serif;
    color: var(--text);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 0.75rem 0;
    letter-spacing: -0.025em;
  }

  .cta-content p {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0 0 1.5rem 0;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 1.5rem;
  }

  .cta-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 0.95rem;
    padding: 0.875rem 1.5rem;
    border-radius: var(--radius);
    border: none;
    transition: all 0.2s ease;
    text-decoration: none;
    letter-spacing: -0.01em;
    margin: 0 0.25rem;
  }

  .cta-btn.primary {
    background: var(--primary);
    color: white;
    box-shadow: var(--shadow-lg);
  }

  .cta-btn.primary:hover {
    background: var(--primary-dark);
    color: white;
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
    text-decoration: none;
  }

  .cta-btn.secondary {
    background: var(--border-light);
    color: var(--text-secondary);
    border: 1px solid var(--border);
  }

  .cta-btn.secondary:hover {
    background: var(--border);
    color: var(--text);
    text-decoration: none;
    transform: translateY(-1px);
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .hero-section {
      padding: 1.75rem 1.5rem;
      margin: 1rem 0;
    }

    .hero-content h1 {
      font-size: 2rem;
    }

    .hero-subtitle {
      font-size: 1rem;
    }

    .hero-description {
      font-size: 0.95rem;
    }

    .about-preview {
      padding: 1.5rem;
    }

    .about-content {
      grid-template-columns: 1fr;
      gap: 1rem;
      text-align: center;
    }

    .about-text h2 {
      font-size: 1.375rem;
    }

    .about-text p {
      font-size: 0.95rem;
    }

    .features-section {
      padding: 1.5rem 0;
    }

    .features-section h2 {
      font-size: 1.5rem;
      margin-bottom: 1.5rem;
    }

    .features-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .feature-card {
      padding: 1.25rem;
    }

    .disclaimers-section {
      padding: 1rem 0;
    }

    .disclaimers-section h2 {
      font-size: 1.375rem;
      margin-bottom: 1rem;
    }

    .disclaimers-grid {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }

    .disclaimer-card {
      padding: 1rem;
    }

    .cta-section {
      padding: 1.5rem;
    }

    .cta-content h2 {
      font-size: 1.375rem;
    }

    .cta-content p {
      font-size: 0.95rem;
      margin-bottom: 1.25rem;
    }

    .cta-btn {
      padding: 0.75rem 1.25rem;
      font-size: 0.9rem;
      margin: 0.25rem;
      display: block;
      width: 100%;
      max-width: 280px;
      margin-left: auto;
      margin-right: auto;
      margin-bottom: 0.5rem;
    }
  }

  @media (max-width: 480px) {
    .hero-section {
      padding: 1.5rem 1.25rem;
    }

    .about-preview {
      padding: 1.25rem;
    }

    .feature-card {
      padding: 1rem;
    }

    .disclaimer-card {
      padding: 0.875rem;
      flex-direction: column;
      text-align: center;
    }

    .disclaimer-icon {
      align-self: center;
    }

    .cta-section {
      padding: 1.25rem;
    }
  }
</style>
