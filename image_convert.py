import os
from PIL import Image

# Explicitly import AVIF plugin for Pillow
import pillow_avif  # noqa: F401

# Make sure you have installed: pip install pillow pillow-avif-plugin


def convert_images_to_webp(source_dir, dest_dir=None, extensions=None, target_size=(600, 450), resize_mode='fit'):
    """
    Batch convert images of specified types to WebP with optional resizing.
    :param source_dir: Directory with source images
    :param dest_dir: Output directory (defaults to source_dir)
    :param extensions: Iterable of file extensions to convert (default: common types)
    :param target_size: Tuple of (width, height) for resizing (default: 600x450)
    :param resize_mode: 'fit' (maintain aspect ratio with padding), 'crop' (maintain aspect ratio, crop excess), 'stretch' (ignore aspect ratio)
    """
    if dest_dir is None:
        dest_dir = source_dir
    if extensions is None:
        extensions = ['.avif', '.jpg', '.jpeg', '.png', '.tiff', '.bmp', '.gif']

    # Create destination directory if it doesn't exist
    os.makedirs(dest_dir, exist_ok=True)

    for filename in os.listdir(source_dir):
        ext = os.path.splitext(filename)[1].lower()
        if ext in extensions:
            src_path = os.path.join(source_dir, filename)
            webp_filename = os.path.splitext(filename)[0] + '.webp'
            webp_path = os.path.join(dest_dir, webp_filename)
            try:
                with Image.open(src_path) as img:
                    # Convert to RGB if necessary (for RGBA, P mode images)
                    if img.mode in ('RGBA', 'LA', 'P'):
                        # Create white background for transparent images
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        if img.mode == 'P':
                            img = img.convert('RGBA')
                        background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                        img = background
                    elif img.mode != 'RGB':
                        img = img.convert('RGB')

                    # Resize image based on mode
                    if resize_mode == 'fit':
                        # Maintain aspect ratio, fit within target size with padding
                        img.thumbnail(target_size, Image.Resampling.LANCZOS)
                        # Create new image with target size and white background
                        new_img = Image.new('RGB', target_size, (255, 255, 255))
                        # Center the resized image
                        x = (target_size[0] - img.width) // 2
                        y = (target_size[1] - img.height) // 2
                        new_img.paste(img, (x, y))
                        img = new_img
                    elif resize_mode == 'crop':
                        # Maintain aspect ratio, crop to fit exact size
                        img_ratio = img.width / img.height
                        target_ratio = target_size[0] / target_size[1]

                        if img_ratio > target_ratio:
                            # Image is wider, crop width
                            new_height = img.height
                            new_width = int(new_height * target_ratio)
                            left = (img.width - new_width) // 2
                            img = img.crop((left, 0, left + new_width, new_height))
                        else:
                            # Image is taller, crop height
                            new_width = img.width
                            new_height = int(new_width / target_ratio)
                            top = (img.height - new_height) // 2
                            img = img.crop((0, top, new_width, top + new_height))

                        img = img.resize(target_size, Image.Resampling.LANCZOS)
                    elif resize_mode == 'stretch':
                        # Ignore aspect ratio, stretch to exact size
                        img = img.resize(target_size, Image.Resampling.LANCZOS)

                    # Save as WebP
                    img.save(webp_path, 'WEBP', quality=95, optimize=True)
                    print(f"Converted and resized: {src_path} -> {webp_path} ({img.width}x{img.height})")
            except Exception as e:
                print(f"Failed to convert {src_path}: {e}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) < 2:
        print("Usage: python image_convert.py <source_dir> [dest_dir] [ext1,ext2,...] [width,height] [resize_mode]")
        print("Examples:")
        print("  python image_convert.py ./images")
        print("  python image_convert.py ./images ./webp")
        print("  python image_convert.py ./images ./webp jpg,png,avif")
        print("  python image_convert.py ./images ./webp jpg,png,avif 600,450")
        print("  python image_convert.py ./images ./webp jpg,png,avif 600,450 crop")
        print("")
        print("Resize modes:")
        print("  fit    - Maintain aspect ratio, fit within target size with white padding (default)")
        print("  crop   - Maintain aspect ratio, crop to exact target size")
        print("  stretch - Ignore aspect ratio, stretch to exact target size")
    else:
        source = sys.argv[1]
        dest = sys.argv[2] if len(sys.argv) > 2 and not sys.argv[2].startswith('.') else None

        # Parse extensions
        exts = None
        if len(sys.argv) > 3 and ',' in sys.argv[3] and not sys.argv[3].replace(',', '').replace('.', '').isdigit():
            exts = ['.' + e.strip().lower().lstrip('.') for e in sys.argv[3].split(',')]

        # Parse target size
        target_size = (600, 450)  # default
        size_arg_index = 4 if exts else 3
        if len(sys.argv) > size_arg_index:
            try:
                size_parts = sys.argv[size_arg_index].split(',')
                if len(size_parts) == 2:
                    target_size = (int(size_parts[0]), int(size_parts[1]))
            except (ValueError, IndexError):
                print(f"Invalid size format: {sys.argv[size_arg_index]}. Using default 600x450.")

        # Parse resize mode
        resize_mode = 'fit'  # default
        mode_arg_index = size_arg_index + 1
        if len(sys.argv) > mode_arg_index:
            mode = sys.argv[mode_arg_index].lower()
            if mode in ['fit', 'crop', 'stretch']:
                resize_mode = mode
            else:
                print(f"Invalid resize mode: {mode}. Using default 'fit'.")

        print(f"Converting images in: {source}")
        print(f"Output directory: {dest or source}")
        print(f"Target size: {target_size[0]}x{target_size[1]}")
        print(f"Resize mode: {resize_mode}")
        print(f"Extensions: {exts or 'all supported formats'}")
        print("-" * 50)

        convert_images_to_webp(source, dest, exts, target_size, resize_mode)