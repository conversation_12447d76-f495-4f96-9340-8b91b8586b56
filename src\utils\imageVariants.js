/**
 * Image Variants Utility
 * Handles selection of appropriate image variants based on context
 */

/**
 * Parse image data from admin panel format
 * Supports both old format (newline-separated URLs) and new format (JSON with variants)
 * @param {string} imageData - Raw image data from textarea
 * @returns {Array} - Array of image objects with variants
 */
export function parseImageData(imageData) {
  // Handle null, undefined, or non-string inputs
  if (!imageData) {
    return [];
  }

  // If imageData is already an array, return it directly
  if (Array.isArray(imageData)) {
    return imageData;
  }

  // Convert to string if it's not already
  const imageDataStr = typeof imageData === 'string' ? imageData : String(imageData);

  if (!imageDataStr.trim()) {
    return [];
  }

  try {
    // Try to parse as JSON (new format)
    const parsed = JSON.parse(imageDataStr);
    if (Array.isArray(parsed)) {
      return parsed;
    }
  } catch (e) {
    // Fall back to old format (newline-separated URLs)
    return imageDataStr
      .split('\n')
      .filter(url => url.trim())
      .map(url => ({
        baseFilename: `legacy-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
        variants: {
          original: url.trim(),
          desktop: url.trim(),
          tablet: url.trim(),
          mobile: url.trim(),
          thumbnail: url.trim()
        }
      }));
  }

  return [];
}

/**
 * Get the best image variant for a given context
 * @param {Object} imageObject - Image object with variants
 * @param {string} context - Context: 'thumbnail', 'mobile', 'tablet', 'desktop', 'original'
 * @returns {string} - Best available image URL
 */
export function getImageVariant(imageObject, context = 'desktop') {
  if (!imageObject || !imageObject.variants || typeof imageObject.variants !== 'object') {
    return null;
  }

  const variants = imageObject.variants;

  // Define fallback order for each context
  const fallbackOrder = {
    thumb: ['thumb', 'mobile', 'tablet', 'desktop', 'original'],
    thumbnail: ['thumb', 'mobile', 'tablet', 'desktop', 'original'], // Alias for thumb
    mobile: ['mobile', 'tablet', 'thumb', 'desktop', 'original'],
    tablet: ['tablet', 'desktop', 'mobile', 'original', 'thumb'],
    desktop: ['desktop', 'original', 'tablet', 'mobile', 'thumb'],
    original: ['original', 'desktop', 'tablet', 'mobile', 'thumb']
  };

  const order = fallbackOrder[context] || fallbackOrder.desktop;

  // Return the first available variant in the fallback order
  for (const variant of order) {
    if (variants[variant]) {
      return variants[variant];
    }
  }

  // If no variants found, return the first available URL
  try {
    const availableUrls = Object.values(variants).filter(url => url);
    return availableUrls.length > 0 ? availableUrls[0] : null;
  } catch (e) {
    return null;
  }
}

/**
 * Get featured image from product data
 * @param {Object} product - Product object
 * @param {string} context - Image context for variant selection
 * @returns {string|null} - Featured image URL or null
 */
export function getFeaturedImageVariant(product, context = 'desktop') {
  if (!product) return null;

  // Parse image data
  const imageData = parseImageData(product.images);
  if (imageData.length === 0) return null;

  // If there's a featured image URL, try to find matching variant
  if (product.featuredImage) {
    // Find the image object that contains the featured image URL
    const featuredImageObj = imageData.find(img => {
      if (!img || !img.variants || typeof img.variants !== 'object') {
        return false;
      }
      try {
        return Object.values(img.variants).includes(product.featuredImage);
      } catch (e) {
        return false;
      }
    });

    if (featuredImageObj) {
      return getImageVariant(featuredImageObj, context);
    }
  }

  // Fall back to first image
  return getImageVariant(imageData[0], context);
}

/**
 * Get all image URLs for a product in a specific context
 * @param {Object} product - Product object
 * @param {string} context - Image context for variant selection
 * @returns {Array} - Array of image URLs
 */
export function getAllImageVariants(product, context = 'desktop') {
  if (!product) return [];

  const imageData = parseImageData(product.images);
  return imageData
    .map(img => getImageVariant(img, context))
    .filter(url => url && typeof url === 'string' && url.trim().length > 0);
}

/**
 * Convert old format product images to new format
 * @param {Array|string} images - Old format images (array of URLs or newline-separated string)
 * @returns {string} - JSON string of new format
 */
export function convertToNewFormat(images) {
  let urls = [];
  
  if (typeof images === 'string') {
    urls = images.split('\n').filter(url => url.trim()).map(url => url.trim());
  } else if (Array.isArray(images)) {
    urls = images.filter(url => url && url.trim()).map(url => url.trim());
  }

  const imageData = urls.map((url, index) => ({
    baseFilename: `converted-${Date.now()}-${index}`,
    variants: {
      original: url,
      desktop: url,
      tablet: url,
      mobile: url,
      thumbnail: url
    }
  }));

  return JSON.stringify(imageData, null, 2);
}

/**
 * Get image count from product data
 * @param {Object} product - Product object
 * @returns {number} - Number of images
 */
export function getImageCount(product) {
  if (!product) return 0;
  
  const imageData = parseImageData(product.images);
  return imageData.length;
}

/**
 * Check if product has multiple images
 * @param {Object} product - Product object
 * @returns {boolean} - True if product has more than one image
 */
export function hasMultipleImages(product) {
  return getImageCount(product) > 1;
}

/**
 * Get responsive image data for a specific image
 * @param {Object} imageObject - Image object with variants
 * @returns {Object} - Responsive image data with srcset and sizes
 */
export function getResponsiveImageData(imageObject) {
  if (!imageObject || !imageObject.variants) {
    return null;
  }

  const variants = imageObject.variants;
  const srcsetParts = [];

  // Build srcset from available variants
  const variantSizes = {
    thumbnail: '150w',
    mobile: '300w',
    tablet: '400w',
    desktop: '500w',
    original: '600w'
  };

  Object.entries(variantSizes).forEach(([variant, size]) => {
    if (variants[variant]) {
      srcsetParts.push(`${variants[variant]} ${size}`);
    }
  });

  return {
    src: variants.desktop || variants.original || Object.values(variants)[0],
    srcset: srcsetParts.join(', '),
    sizes: '(max-width: 480px) 300px, (max-width: 768px) 400px, (max-width: 1200px) 500px, 500px'
  };
}
