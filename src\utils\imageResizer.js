/**
 * Client-side Image Resizing Utility
 * Generates multiple image variants for optimal performance
 */

export class ImageResizer {
  constructor() {
    // Define image variants based on your 600x450 source images
    this.variants = {
      thumbnail: { width: 150, height: 113, suffix: '-thumb', quality: 0.8 },
      mobile: { width: 300, height: 225, suffix: '-mobile', quality: 0.85 },
      tablet: { width: 400, height: 300, suffix: '-tablet', quality: 0.85 },
      desktop: { width: 500, height: 375, suffix: '-desktop', quality: 0.9 },
      original: { width: 600, height: 450, suffix: '-original', quality: 0.95 }
    };
  }

  /**
   * Resize a single image file to all variants
   * @param {File} file - The original image file
   * @returns {Promise<Object>} - Object containing all resized variants
   */
  async resizeImage(file) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      img.onload = async () => {
        try {
          const variants = {};
          const baseFilename = this.getBaseFilename(file.name);
          
          // Generate each variant
          for (const [variantName, config] of Object.entries(this.variants)) {
            const resizedBlob = await this.createVariant(img, canvas, ctx, config);
            const filename = `${baseFilename}${config.suffix}.webp`;
            
            variants[variantName] = {
              blob: resizedBlob,
              filename: filename,
              width: config.width,
              height: config.height,
              size: resizedBlob.size
            };
          }
          
          resolve({
            originalFilename: file.name,
            baseFilename: baseFilename,
            variants: variants,
            totalVariants: Object.keys(variants).length
          });
        } catch (error) {
          reject(error);
        }
      };
      
      img.onerror = () => {
        reject(new Error(`Failed to load image: ${file.name}`));
      };
      
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Create a single variant of the image
   * @param {HTMLImageElement} img - Source image
   * @param {HTMLCanvasElement} canvas - Canvas element
   * @param {CanvasRenderingContext2D} ctx - Canvas context
   * @param {Object} config - Variant configuration
   * @returns {Promise<Blob>} - Resized image blob
   */
  async createVariant(img, canvas, ctx, config) {
    // Set canvas dimensions
    canvas.width = config.width;
    canvas.height = config.height;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Enable image smoothing for better quality
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
    
    // Calculate source dimensions to maintain aspect ratio
    const sourceAspectRatio = img.width / img.height;
    const targetAspectRatio = config.width / config.height;
    
    let sourceX = 0, sourceY = 0, sourceWidth = img.width, sourceHeight = img.height;
    
    // Crop source image to match target aspect ratio (center crop)
    if (sourceAspectRatio > targetAspectRatio) {
      // Source is wider, crop width
      sourceWidth = img.height * targetAspectRatio;
      sourceX = (img.width - sourceWidth) / 2;
    } else if (sourceAspectRatio < targetAspectRatio) {
      // Source is taller, crop height
      sourceHeight = img.width / targetAspectRatio;
      sourceY = (img.height - sourceHeight) / 2;
    }
    
    // Draw the resized image
    ctx.drawImage(
      img,
      sourceX, sourceY, sourceWidth, sourceHeight,
      0, 0, config.width, config.height
    );
    
    // Convert to blob with specified quality
    return new Promise((resolve) => {
      canvas.toBlob(resolve, 'image/webp', config.quality);
    });
  }

  /**
   * Process multiple files
   * @param {FileList|Array} files - Array of image files
   * @param {Function} progressCallback - Progress callback function
   * @returns {Promise<Array>} - Array of processed image results
   */
  async processFiles(files, progressCallback = null) {
    const results = [];
    const totalFiles = files.length;
    
    for (let i = 0; i < totalFiles; i++) {
      const file = files[i];
      
      if (progressCallback) {
        progressCallback({
          current: i + 1,
          total: totalFiles,
          filename: file.name,
          stage: 'processing'
        });
      }
      
      try {
        const result = await this.resizeImage(file);
        results.push({
          success: true,
          originalFile: file,
          ...result
        });
      } catch (error) {
        results.push({
          success: false,
          originalFile: file,
          error: error.message
        });
      }
    }
    
    return results;
  }

  /**
   * Get base filename without extension
   * @param {string} filename - Original filename
   * @returns {string} - Base filename
   */
  getBaseFilename(filename) {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 8);
    const baseName = filename.replace(/\.[^/.]+$/, '').replace(/[^a-zA-Z0-9-_]/g, '-');
    return `${baseName}-${timestamp}-${randomId}`;
  }

  /**
   * Calculate total size of all variants
   * @param {Object} variants - Variants object
   * @returns {number} - Total size in bytes
   */
  getTotalSize(variants) {
    return Object.values(variants).reduce((total, variant) => total + variant.size, 0);
  }

  /**
   * Format file size for display
   * @param {number} bytes - Size in bytes
   * @returns {string} - Formatted size string
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Validate image file
   * @param {File} file - File to validate
   * @returns {Object} - Validation result
   */
  validateFile(file) {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.'
      };
    }
    
    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'File size too large. Maximum size is 10MB.'
      };
    }
    
    return { valid: true };
  }
}

// Export singleton instance
export const imageResizer = new ImageResizer();
