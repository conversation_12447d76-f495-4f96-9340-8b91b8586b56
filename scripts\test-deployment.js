#!/usr/bin/env node

/**
 * Test script to verify the complete deployment pipeline
 * Run with: node scripts/test-deployment.js
 */

const BASE_URL = process.env.TEST_URL || 'http://localhost:4321';

async function testEndpoint(url, options = {}) {
  try {
    console.log(`Testing: ${url}`);
    const response = await fetch(url, options);
    const data = await response.json();
    
    console.log(`Status: ${response.status}`);
    console.log(`Response:`, JSON.stringify(data, null, 2));
    console.log('---');
    
    return { success: response.ok, data, status: response.status };
  } catch (error) {
    console.error(`Error testing ${url}:`, error.message);
    console.log('---');
    return { success: false, error: error.message };
  }
}

async function runTests() {
  console.log('🚀 Testing Cheers Marketplace Deployment Pipeline\n');
  console.log(`Base URL: ${BASE_URL}\n`);

  // Test 1: GitHub Configuration
  console.log('1. Testing GitHub Configuration...');
  const githubTest = await testEndpoint(`${BASE_URL}/api/github-config`);
  
  if (githubTest.success && githubTest.data.configured) {
    console.log('✅ GitHub integration is configured');
    if (githubTest.data.connected) {
      console.log('✅ GitHub connection successful');
      console.log(`Repository: ${githubTest.data.repository?.fullName}`);
    } else {
      console.log('❌ GitHub connection failed');
      console.log(`Error: ${githubTest.data.error}`);
    }
  } else {
    console.log('⚠️  GitHub integration not configured');
    console.log('This means automatic deployment from admin panel will not work');
  }
  console.log('');

  // Test 2: Products API
  console.log('2. Testing Products API...');
  const productsTest = await testEndpoint(`${BASE_URL}/api/products.json`);
  
  if (productsTest.success) {
    console.log('✅ Products API working');
    console.log(`Found ${productsTest.data.length} products`);
  } else {
    console.log('❌ Products API failed');
  }
  console.log('');

  // Test 3: Sync Products API (GET)
  console.log('3. Testing Sync Products API (GET)...');
  const syncGetTest = await testEndpoint(`${BASE_URL}/api/sync-products`);
  
  if (syncGetTest.success) {
    console.log('✅ Sync Products API (GET) working');
    console.log(`Products count: ${syncGetTest.data.count}`);
  } else {
    console.log('❌ Sync Products API (GET) failed');
  }
  console.log('');

  // Test 4: Build Hook API
  console.log('4. Testing Build Hook API...');
  const buildHookTest = await testEndpoint(`${BASE_URL}/api/build-hook`);
  
  if (buildHookTest.success) {
    console.log('✅ Build Hook API working');
    console.log(`Configured: ${buildHookTest.data.configured}`);
  } else {
    console.log('❌ Build Hook API failed');
  }
  console.log('');

  // Test 5: Admin Panel Access
  console.log('5. Testing Admin Panel Access...');
  try {
    const adminResponse = await fetch(`${BASE_URL}/admin?admin=1`);
    if (adminResponse.ok) {
      console.log('✅ Admin panel accessible');
    } else {
      console.log('❌ Admin panel not accessible');
    }
  } catch (error) {
    console.log('❌ Admin panel test failed:', error.message);
  }
  console.log('');

  // Summary
  console.log('📋 Test Summary:');
  console.log('================');
  
  if (githubTest.success && githubTest.data.configured && githubTest.data.connected) {
    console.log('✅ GitHub Integration: READY');
    console.log('   → Admin panel can automatically deploy changes');
  } else if (githubTest.success && githubTest.data.configured) {
    console.log('⚠️  GitHub Integration: CONFIGURED BUT NOT CONNECTED');
    console.log('   → Check your GitHub token and repository access');
  } else {
    console.log('❌ GitHub Integration: NOT CONFIGURED');
    console.log('   → Set up environment variables for automatic deployment');
  }
  
  if (productsTest.success && syncGetTest.success) {
    console.log('✅ Product Management: WORKING');
  } else {
    console.log('❌ Product Management: ISSUES DETECTED');
  }
  
  if (buildHookTest.success) {
    console.log('✅ Build Hook API: AVAILABLE');
  } else {
    console.log('❌ Build Hook API: NOT WORKING');
  }

  console.log('\n🔗 Next Steps:');
  if (!githubTest.data?.configured) {
    console.log('1. Set up GitHub integration following docs/GITHUB_INTEGRATION_SETUP.md');
  }
  if (!githubTest.data?.connected && githubTest.data?.configured) {
    console.log('1. Check your GitHub token permissions and repository access');
  }
  console.log('2. Test the admin panel at:', `${BASE_URL}/admin?admin=1`);
  console.log('3. Try adding/editing a product and clicking "Sync & Deploy"');
  console.log('4. Monitor deployment in Cloudflare Pages dashboard');
}

// Run the tests
runTests().catch(console.error);
